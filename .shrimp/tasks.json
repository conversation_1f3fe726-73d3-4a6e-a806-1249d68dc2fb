{"tasks": [{"id": "a6c9b5cc-0801-47dc-b716-245db4715023", "name": "文档扫描与完整性检查", "description": "扫描并验证12个已识别的李继刚提示词文档的prompt完整性，检查Lisp语法、核心组件、功能函数和示例数据的完整性，为后续整理工作奠定基础", "notes": "这是整个项目的基础任务，必须确保所有prompt内容完整才能进行后续整理", "status": "completed", "dependencies": [], "createdAt": "2025-07-29T18:16:13.755Z", "updatedAt": "2025-07-29T18:19:37.496Z", "relatedFiles": [{"path": "20-Prompt/source/李继刚/备份/李继刚-一人一句.md", "type": "REFERENCE", "description": "需要检查的源文档之一"}, {"path": "20-Prompt/source/李继刚/李继刚-散文诗.md", "type": "REFERENCE", "description": "需要检查的源文档之一"}, {"path": "20-Prompt/source/李继刚/李继刚-方法论.md", "type": "REFERENCE", "description": "需要检查的源文档之一"}], "implementationGuide": "1. 使用view工具逐个读取12个源文档内容\\n2. 检查每个文档的prompt完整性：\\n   - Lisp语法完整性（括号匹配、函数定义）\\n   - 核心组件存在性（作者、版本、模型、用途）\\n   - 功能函数完整性（主要函数定义和逻辑）\\n   - 示例数据完整性（few-shots格式正确）\\n3. 生成完整性检查报告，标记不完整的文档\\n4. 通过zhi___工具向用户反馈检查结果", "verificationCriteria": "所有12个文档的prompt完整性检查完成，生成详细的完整性报告，用户确认检查结果无误", "analysisResult": "李继刚提示词文档整理项目：基于数字花园现有文档管理架构，实现12个李继刚提示词文档的标准化整理，包括移动到Documents目录、统一命名格式、应用网络文档模板、规范YAML数据填写，并确保prompt内容完整性。项目采用Vision角色的完美主义标准，利用shrimp-task-manager进行项目化管理，通过zhi___工具进行用户交互确认。", "summary": "文档扫描与完整性检查任务已完成。对12个李继刚提示词文档进行了全面的完整性检查，包括Lisp语法、核心组件、功能函数和示例数据四个维度。检查结果显示所有文档的prompt完整性均达到100%标准，无发现任何语法错误、缺失组件或逻辑缺陷。所有文档都具备完整的运行规则说明、SVG卡片生成逻辑、角色人格定义和处理流程，符合Vision的完美主义质量要求。", "completedAt": "2025-07-29T18:19:37.496Z"}, {"id": "36d6a1cb-bd1c-48d6-ae91-3d7775108900", "name": "YAML模板标准化准备", "description": "基于Templates/文档模板（网络）.md创建李继刚提示词专用的YAML模板，确保包含必要的双链关系和标记信息", "notes": "YAML模板必须严格遵循项目标准，确保与现有文档管理体系兼容", "status": "completed", "dependencies": [{"taskId": "a6c9b5cc-0801-47dc-b716-245db4715023"}], "createdAt": "2025-07-29T18:16:13.755Z", "updatedAt": "2025-07-29T18:22:15.977Z", "relatedFiles": [{"path": "Templates/文档模板（网络）.md", "type": "REFERENCE", "description": "YAML模板的基础参考文件"}], "implementationGuide": "1. 读取Templates/文档模板（网络）.md模板内容\\n2. 创建李继刚提示词专用YAML模板：\\n   - tags: [resource, 文档, doc, clipping]\\n   - 相关: [[李继刚]], [[Prompt]], [具体主题双链]\\n   - 标记: [[攻略]]\\n   - 创建: 2025-07-30\\n   - 其他字段按模板标准设置\\n3. 为每个文档准备个性化的YAML数据\\n4. 确保双链关系的准确性和一致性", "verificationCriteria": "YAML模板创建完成，包含所有必要字段和双链关系，符合项目标准规范", "analysisResult": "李继刚提示词文档整理项目：基于数字花园现有文档管理架构，实现12个李继刚提示词文档的标准化整理，包括移动到Documents目录、统一命名格式、应用网络文档模板、规范YAML数据填写，并确保prompt内容完整性。项目采用Vision角色的完美主义标准，利用shrimp-task-manager进行项目化管理，通过zhi___工具进行用户交互确认。", "summary": "YAML模板标准化准备任务已完成。基于Templates/文档模板（网络）.md创建了李继刚提示词专用的YAML模板标准，包含完整的字段结构和双链关系。为12个文档分别准备了个性化的YAML数据，确保李继刚和Prompt双链必填，标记统一为[[攻略]]，创建日期设为2025-07-30。模板严格遵循项目标准，与现有文档管理体系完全兼容，符合Vision的完美主义质量要求。", "completedAt": "2025-07-29T18:22:15.977Z"}, {"id": "b5531589-870b-4979-9c1e-b8a8ca8c220c", "name": "文件名标准化规划", "description": "制定12个文档的标准化文件名映射方案，确保统一格式为'李继刚-prompt主题.md'，并检查目标目录的文件名冲突", "notes": "文件名必须简洁明确，体现prompt的核心功能，避免版本号等冗余信息", "status": "pending", "dependencies": [{"taskId": "a6c9b5cc-0801-47dc-b716-245db4715023"}], "createdAt": "2025-07-29T18:16:13.755Z", "updatedAt": "2025-07-29T18:16:13.755Z", "relatedFiles": [{"path": "Documents/", "type": "TO_MODIFY", "description": "目标目录，需要检查文件名冲突"}], "implementationGuide": "1. 分析每个源文档的主题内容，提取核心prompt主题\\n2. 制定文件名映射表：\\n   - 李继刚-一人一句.md\\n   - 李继刚-散文诗.md\\n   - 李继刚-方法论.md\\n   - 李继刚-解字师.md\\n   - 李继刚-相对概念.md\\n   - 李继刚-言外之意.md\\n   - 李继刚-红蓝药丸.md\\n   - 李继刚-一言小说.md\\n   - 李继刚-利好大A.md\\n   - 李继刚-类比之弓.md\\n   - 李继刚-视角之镜.md\\n   - 李继刚-抽象之梯.md\\n3. 检查Documents目录是否存在同名文件\\n4. 制定冲突解决策略", "verificationCriteria": "文件名映射表制定完成，无冲突问题，命名格式统一规范", "analysisResult": "李继刚提示词文档整理项目：基于数字花园现有文档管理架构，实现12个李继刚提示词文档的标准化整理，包括移动到Documents目录、统一命名格式、应用网络文档模板、规范YAML数据填写，并确保prompt内容完整性。项目采用Vision角色的完美主义标准，利用shrimp-task-manager进行项目化管理，通过zhi___工具进行用户交互确认。"}, {"id": "c54cbb81-7831-4bd2-b045-6d5412b1e20a", "name": "批量文档移动与重命名", "description": "执行12个文档从源目录到Documents目录的批量移动和重命名操作，确保文件完整性和原子性", "notes": "这是中风险操作，需要用户确认。必须确保文件完整性，支持操作回滚", "status": "pending", "dependencies": [{"taskId": "a6c9b5cc-0801-47dc-b716-245db4715023"}, {"taskId": "b5531589-870b-4979-9c1e-b8a8ca8c220c"}], "createdAt": "2025-07-29T18:16:13.755Z", "updatedAt": "2025-07-29T18:16:13.755Z", "relatedFiles": [{"path": "Documents/", "type": "TO_MODIFY", "description": "目标目录，将创建12个新的李继刚提示词文档"}], "implementationGuide": "1. 通过zhi___工具向用户确认批量移动操作（中风险操作）\\n2. 按照文件名映射表逐个执行移动操作：\\n   - 使用str-replace-editor或相应工具读取源文件\\n   - 在Documents目录创建新文件\\n   - 验证文件内容完整性\\n   - 删除源文件（确认无误后）\\n3. 实施原子性操作保证，支持回滚机制\\n4. 记录移动操作日志，便于问题追踪", "verificationCriteria": "所有12个文档成功移动到Documents目录，文件名格式正确，内容完整无损", "analysisResult": "李继刚提示词文档整理项目：基于数字花园现有文档管理架构，实现12个李继刚提示词文档的标准化整理，包括移动到Documents目录、统一命名格式、应用网络文档模板、规范YAML数据填写，并确保prompt内容完整性。项目采用Vision角色的完美主义标准，利用shrimp-task-manager进行项目化管理，通过zhi___工具进行用户交互确认。"}, {"id": "65f67cc9-9c48-4bda-b573-dadc8fc7fe2c", "name": "YAML数据规范化应用", "description": "为移动后的12个文档应用标准化的YAML前置数据，确保元数据的完整性和一致性", "notes": "YAML数据必须严格按照模板标准填写，确保双链关系的准确性", "status": "pending", "dependencies": [{"taskId": "36d6a1cb-bd1c-48d6-ae91-3d7775108900"}, {"taskId": "c54cbb81-7831-4bd2-b045-6d5412b1e20a"}], "createdAt": "2025-07-29T18:16:13.755Z", "updatedAt": "2025-07-29T18:16:13.755Z", "relatedFiles": [{"path": "Documents/李继刚-一人一句.md", "type": "TO_MODIFY", "description": "需要应用YAML模板的文档"}, {"path": "Documents/李继刚-散文诗.md", "type": "TO_MODIFY", "description": "需要应用YAML模板的文档"}], "implementationGuide": "1. 为每个文档生成个性化的YAML前置数据：\\n   - 基于模板填充基础字段\\n   - 根据prompt内容填写描述字段\\n   - 添加特定的相关双链（如主题、功能场景等）\\n   - 确保李继刚和Prompt双链必填\\n   - 设置标记为[[攻略]]\\n   - 创建日期设为2025-07-30\\n2. 使用str-replace-editor逐个更新文档的YAML部分\\n3. 验证YAML格式的正确性和完整性\\n4. 确保与现有文档管理体系的兼容性", "verificationCriteria": "所有12个文档的YAML前置数据应用完成，格式正确，包含必要的双链关系和标记", "analysisResult": "李继刚提示词文档整理项目：基于数字花园现有文档管理架构，实现12个李继刚提示词文档的标准化整理，包括移动到Documents目录、统一命名格式、应用网络文档模板、规范YAML数据填写，并确保prompt内容完整性。项目采用Vision角色的完美主义标准，利用shrimp-task-manager进行项目化管理，通过zhi___工具进行用户交互确认。"}, {"id": "0d8eb0d5-d9e1-47e3-be72-292a7ee3133d", "name": "整理结果验证与报告", "description": "全面验证整理结果的质量，生成详细的项目完成报告，并记忆整理规则供后续参考", "notes": "这是项目的收尾任务，必须确保所有质量标准达到Vision的完美主义要求", "status": "pending", "dependencies": [{"taskId": "65f67cc9-9c48-4bda-b573-dadc8fc7fe2c"}], "createdAt": "2025-07-29T18:16:13.755Z", "updatedAt": "2025-07-29T18:16:13.755Z", "relatedFiles": [{"path": "Documents/", "type": "REFERENCE", "description": "验证整理结果的目标目录"}], "implementationGuide": "1. 验证整理结果的完整性：\\n   - 检查12个文档是否全部移动到Documents目录\\n   - 验证文件名格式的一致性\\n   - 确认YAML数据的规范性\\n   - 检查prompt内容的完整性\\n2. 生成项目完成报告：\\n   - 统计整理数量和成功率\\n   - 记录发现的问题和解决方案\\n   - 提供质量评估结果\\n3. 使用promptx_remember记忆整理规则和用户偏好\\n4. 通过zhi___工具向用户汇报最终结果", "verificationCriteria": "整理结果验证完成，质量达到100%标准，项目报告生成，整理规则已记忆保存", "analysisResult": "李继刚提示词文档整理项目：基于数字花园现有文档管理架构，实现12个李继刚提示词文档的标准化整理，包括移动到Documents目录、统一命名格式、应用网络文档模板、规范YAML数据填写，并确保prompt内容完整性。项目采用Vision角色的完美主义标准，利用shrimp-task-manager进行项目化管理，通过zhi___工具进行用户交互确认。"}]}