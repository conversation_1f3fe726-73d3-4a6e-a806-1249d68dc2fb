> 字数 1904，阅读大约需 7 分钟

## 🔥 【Augment】 解决Augment 在VScode、<PERSON>ursor、jeb系列Idea内无法登录报错：Sign in failed. If you have a firewall, please add

## 前言

群里好多小伙伴反馈说 `Augment` 在VScode、<PERSON>ursor、jeb系列Idea内无法登录报错：`Sign in failed. If you have a firewall, please add`，我看见论坛也有不少，我就跟了一下源码找了一下原因

❌ 错误码

> Sign in failed. If you have a firewall, please add "https://d*.api.augmentcode.com/" to your allowlist.

## 🔍 1. 错误消息来源分析

### 1.1 主要错误消息定义

**位置**: `messages/AugmentBundle.properties`

```
# 认证失败相关消息auth.signInDialog.title=Augment Sign Inauth.signInDialog.body="Signing in to Augment..."auth.signInErrorDialog.body=Failed to sign in. Please try again and reach out to Augment support, if the issue continues.augment.status.unauthorized.tooltip=Authentication Failedaugment.status.get-model-info-failed.tooltip=Cannot connect to Augment
```

### 1.2 网络错误处理

**位置**: `com/augmentcode/intellij/api/AugmentHttpClient.java`

```
private final Void wrapNetworkError(String path, String requestId, Exception e) {    logger.warn("Failed to call " + path + " (" + requestId + ")", (Throwable)e);    this.reportError(path, requestId, e);    throw new IllegalStateException("Failed to make network call to " + path + " with request ID " + requestId, (Throwable)e);}
```

### 1.3 HTTP客户端超时配置

**位置**: `com/augmentcode/intellij/api/AugmentHttpClient.java`

在HTTP请求构建时，同时设置：

```
//伪代码public int connectionTimeoutMs = 5000;              // 5秒 (连接超时) public int SocketTimeoutMs = 5000;              // 5秒 (连接超时)$this$timeout.setRequestTimeoutMillis(connectionTimeoutMs);  // 请求超时 5秒$this$timeout.setSocketTimeoutMillis(SocketTimeoutMs);   // Socket超时 5秒
```

## ⏱️ 2. 超时导致登录失败的具体场景

### 2.1 OAuth认证流程中的超时点

1. 1. **授权端点连接**: `https://auth.augmentcode.com/authorize`
    

- • 连接超时: 5秒 (`LatencyDetector.DEFAULT_TIMEOUT`)
    
- • 读取超时: 5秒
    

1. 2. **Token端点请求**: `<tenant_url>/token`
    

- • 请求超时: 30秒 (`DEFUALT_STREAM_TIMEOUT_MS`)
    
- • Socket超时: 30秒
    

1. 3. **回调处理**: `http://127.0.0.1:<port>/api/augment/auth/result`
    

- • 本地服务器超时处理
    

## 🔥 3. 防火墙相关错误分析

### 防火墙检测逻辑

**位置**: `LatencyDetector.java`

```
private long measureHttpLatency(@NotNull String endpoint) {    try {        URL url = new URL("https://" + endpoint + "/");        HttpURLConnection connection = (HttpURLConnection) url.openConnection();        connection.setRequestMethod("HEAD");        connection.setConnectTimeout(DEFAULT_TIMEOUT);  // 5秒        connection.setReadTimeout(DEFAULT_TIMEOUT);     // 5秒                int responseCode = connection.getResponseCode();        // 如果这里超时或连接失败，通常是防火墙问题            } catch (IOException e) {        return -1;  // 连接失败，可能是防火墙阻止    }}
```

### 防火墙相关问题分析

#### 需要访问的域名和端口

```
✅ 必须允许的连接:- auth.augmentcode.com:443 (HTTPS) - OAuth认证服务器- *.api.augmentcode.com:443 (HTTPS) - API端点- 127.0.0.1:随机端口 - OAuth回调服务器🚫 防火墙可能阻止的连接:- 出站HTTPS连接到auth.augmentcode.com- 入站连接到本地回调端口- DNS解析augmentcode.com域名
```

### 🛠️ 解决方案和建议

### 防火墙配置

```
# Windows防火墙netsh advfirewall firewall add rule name="Augment HTTPS" dir=out action=allow protocol=TCP remoteport=443# 企业防火墙白名单*.augmentcode.comauth.augmentcode.comd1.api.augmentcode.com - d20.api.augmentcode.com#检查防火墙设置Windows: 控制面板 → 系统和安全 → Windows Defender 防火墙
```

## 🚨 4. 登录失败的具体触发条件

### 4.1 网络层面的失败

1. 1. **连接超时** (5秒内无法建立连接)
    

```
connection.setConnectTimeout(DEFAULT_TIMEOUT); // 5秒
```

1. 2. **读取超时** (5秒内无法读取响应)
    

```
connection.setReadTimeout(DEFAULT_TIMEOUT); // 5秒
```

1. 3. **DNS解析失败**
    

- • 无法解析 `auth.augmentcode.com`
    
- • 无法解析 tenant URL
    

### 4.2 OAuth流程中的失败点

**位置**: `com/augmentcode/intellij/auth/AugmentOAuthService.java`

```
public OAuthService.OAuthResult<AugmentCredentials> handleOAuthServerCallback(...) {    // 1. 检查授权URL是否匹配    if (!Intrinsics.areEqual(path, request.getRequest().getAuthorizationCodeUrl().getPath())) {        request.getResult().completeExceptionally(new RuntimeException("Unexpected authorization URL"));        logger.warn("Unexpected authorization URL");        return null;    }        // 2. 检查是否收到授权码    if (code == null) {        request.getResult().completeExceptionally(new RuntimeException("No code provided"));        logger.warn("No code provided");        return null;    }        // 3. 检查是否收到tenant URL    if (tenantURL == null) {        request.getResult().completeExceptionally(new RuntimeException("No tenant URL provided"));        logger.warn("No tenant URL provided");        return null;    }}
```

## 🏆 结论

**"Sign in failed. If you have a firewall"错误主要由以下原因导致**:

1. 1. **最短超时配置 (5秒)** 导致连接失败
    
2. 2. **防火墙阻止** HTTPS连接到认证服务器
    
3. 3. **网络延迟** 超过配置的超时时间
    
4. 4. **DNS解析问题** 无法解析认证域名,
    

**解决方案优先级**:

1. 1. 检查防火墙设置，允许访问 `*.augmentcode.com` 下面清理工具可以全部指向最低 d*.api 的端点
    
2. 2. 增加连接超时时间 (从5秒增加到10-15秒)，后续插件支持~
    
3. 3. 🔍 **深入**: 检查代理设置和DNS解析
    
4. 4. 查看IDE日志获取详细错误信息
    

## 清理工具新增端点最低延迟测速和hosts指向 v3.5

1. 1. **版本更新说明**：
    

- • 强调本次更新的核心功能：优化了 API 域名解析策略
    
- • 说明新策略将所有 d1~d20.api.augmentcode.com 域名统一指向延迟最低的服务器IP
    
- • 突出修复效果：解决 VSCode、Cursor、JetBrains 系列 IDE 中 Augment 插件无法登录的问题
    
- •
    

1. 1. **问题根因说明**：
    

- • 原问题：API 响应过慢或无响应导致插件认为连接失败
    
- • 解决方案：通过 hosts 优化确保所有请求都路由到最快的服务器
    
- • 预期效果：显著提升 API 响应速度，解决登录超时问题
    

> https://github.com/yuaotian/go-augment-cleaner/releases

**关注我，获取更多AI工具使用技巧** 📱

还在为各种AI插件的奇葩问题发愁？

关注【煎饼果子卷AI】，每周分享：

- • 🔥 最新AI工具评测
    
- • 🛠️ 实用问题解决方案
    
- • 💡 提升编程效率的小技巧  
    记得设置星标⭐，不错过每一篇干货！
    

---

PS：写这篇文章真的累死我了...为了扒源码双开java插件和vscode的插件，vscode插件还是混淆的看着头大😭