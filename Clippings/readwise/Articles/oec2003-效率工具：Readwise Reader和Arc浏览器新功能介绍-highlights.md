---
人员: 
  - "[[oec2003]]"
tags:
  - articles
日期: 2024-04-23
时间: 2024-08-04 09:52:35.755419+00:00
相关:
  - "[[readewise/reader]]"
  - "[[summary]]"

链接: https://mp.weixin.qq.com/s/7dzfw2IBAEn8_nDUsHEtCA
附件: https://mmbiz.qpic.cn/mmbiz_jpg/eqcwh4cqlibHojLp1xiaW2xaBauARc6jvuFzeWCP6lb0eTibBZ4icUfFEpU7F9n8r0ViaFCVibDLfTVkzPFlfPDiapRmQ/0?wx_fmt=jpeg)
---
## Document Note

## Summary

比如：搜索「dotnet8下载页面」或者「冯威博客」，按下 Shift + Enter ，会出现下图的提示文本：Searching the web for you...

## Full Document
[[Full Document Contents/Articles/oec2003-效率工具：Readwise Reader和Arc浏览器新功能介绍.md|See full document content →]]

## Highlights
- {#- 简体中文摘要与重点提取 -#} 
  {#- 下面的 Prompt 会将文档缩减成一个 500 字内的摘要，并根据你的 highlight 进行重点摘要。-#} 
  请使用 500 字以内，以简体中文总结以下文本： 
  """ 
  标题：{{ document.title }} 
  作者：{{ document.author }}  
  来源：{{ document.domain }} 
  另外，在阅读此文章时，我对以下部分进行了高亮，认为这些是文章的重点，给你学习参考： 
  {% for highlight in document.highlights %} 
  - {{ highlight.content }} 
  {% endfor %} 
  {#- 下面的 if-else 逻辑检查文档的长度。如果文档较长，它将使用关键句子以避免超出 GPT 提示窗口的限制。我们强烈建议除非您知道自己在做什么，否则不要更改此设置。-#} 
  {% if (document.content | count_tokens) > 2000 %} 
  {{ document.content | central_sentences | join('\n\n') }} 
  {% else %} 
  {{ document.content }} 
  {% endif %} 
  """ 
  重要提示：请不要超过 500 字。每句话应该简洁易读；关于中文的排版原则：在中文和英文或数字之间，要有一个半角空白，例如：Apple 手机；3 个 AI 工具。 
  另外，在文章总结下方换行后，基于本文重点，请创建 3 个有关问题及其答案。每个问题应能帮助深入理解文章的关键概念，并加强对重点的印象。请注意，在问题的这一行上，使用粗体的 markdown 格式，或者 <strong> 的 html 标签来更让文字显眼。 
  {#- 创建问答 -#} 
  **问题 1：{# 基于重点内容构建的问题 #}** 
  答案：{# 对应问题 1 的答案，应该包含对应的重点内容 #} 
  **问题 2：{# 同上 #}**  
  答案：{# 对应问题 2 的答案，应该包含对应的重点内容 #} 
  **问题 3：{# 同上 #}** 
  答案：{# 对应问题 3 的答案，应该包含对应的重点内容 #} ([View Highlight](https://read.readwise.io/read/01j4b5fyah5bsbwgaz60164dmk))
- {#- 简体中文摘要与重点提取 -#} 
  {#- 下面的 Prompt 会将文档缩减成一个 500 字内的摘要，并根据你的 highlight 进行重点摘要。-#} 
  请使用 500 字以内，以简体中文总结以下文本： 
  """ 
  标题：{{ document.title }} 
  作者：{{ document.author }}  
  来源：{{ document.domain }} 
  另外，在阅读此文章时，我对以下部分进行了高亮，认为这些是文章的重点，给你学习参考： 
  {% for highlight in document.highlights %} 
  - {{ highlight.content }} 
  {% endfor %} 
  {#- 下面的 if-else 逻辑检查文档的长度。如果文档较长，它将使用关键句子以避免超出 GPT 提示窗口的限制。我们强烈建议除非您知道自己在做什么，否则不要更改此设置。-#} 
  {% if (document.content | count_tokens) > 2000 %} 
  {{ document.content | central_sentences | join('\n\n') }} 
  {% else %} 
  {{ document.content }} 
  {% endif %} 
  """ 
  重要提示：请不要超过 500 字。每句话应该简洁易读；关于中文的排版原则：在中文和英文或数字之间，要有一个半角空白，例如：Apple 手机；3 个 AI 工具。 
  另外，在文章总结下方换行后，基于本文重点，请创建 3 个有关问题及其答案。每个问题应能帮助深入理解文章的关键概念，并加强对重点的印象。请注意，在问题的这一行上，使用粗体的 markdown 格式，或者 <strong> 的 html 标签来更让文字显眼。 
  {#- 创建问答 -#} 
  **问题 1：{# 基于重点内容构建的问题 #}** 
  答案：{# 对应问题 1 的答案，应该包含对应的重点内容 #} 
  **问题 2：{# 同上 #}**  
  答案：{# 对应问题 2 的答案，应该包含对应的重点内容 #} 
  **问题 3：{# 同上 #}** 
  答案：{# 对应问题 3 的答案，应该包含对应的重点内容 #} ([View Highlight](https://read.readwise.io/read/01j4b4nxketzwa8z46rjyakdsk))
- Reader ([View Highlight](https://read.readwise.io/read/01j4b2wgj0rm4qqs54zxch0a5g))
- 1、在 Reader 中阅读文章时，右边栏会有 summary 对文章进行总结，不过之前的版本总结很差，几乎没法用，要么是英文、要么很简短。 ([View Highlight](https://read.readwise.io/read/01j4b2wjbq7a699pp3pnyt3zwa))
- 上面的提示语可以达到以下三个效果： ([View Highlight](https://read.readwise.io/read/01j4b5g257re8rwrdn8gzagfsm))
- • 根据文章内容生产 500 字以内的总结性文字。 ([View Highlight](https://read.readwise.io/read/01j4b5g3t1aj6ysrkxrke3qqw9))
- 2、现在的版本中推出了一个 Ghostreader prompts 的功能，可以自定义提示语，点击下图 Customize 进行设置。 ([View Highlight](https://read.readwise.io/read/01j4b2wqwny0j43k29kdf301hk))
- • 可以将你在文章中划线部分进行重点参考。 ([View Highlight](https://read.readwise.io/read/01j4b5g7fsnp8t18zajhgfaet0))
- • 针对文章内容提出 3 个问题，并给出答案。 ([View Highlight](https://read.readwise.io/read/01j4b5g92szw3bxqz1k9gx2jwk))
- 5、默认情况下使用的是 GPT-3.5 Turbo ,如果想要使用 GPT-4 ，需要使用自己的 API key 。 ([View Highlight](https://read.readwise.io/read/01j4b5gan9x15rke8eced6fx2s))
- 3、点击下图中的 Edit prompt 进行提示词的编辑。 ([View Highlight](https://read.readwise.io/read/01j4b2x27rfff9ftyfa7tnaqy7))
- 4、编辑的内容如下： ([View Highlight](https://read.readwise.io/read/01j4b2x3v8nawg9mvdhz9np2jh))
- {#- 简体中文摘要与重点提取 -#} 
  {#- 下面的 Prompt 会将文档缩减成一个 500 字内的摘要，并根据你的 highlight 进行重点摘要。-#} 
  请使用 500 字以内，以简体中文总结以下文本： 
  """ 
  标题：{{ document.title }} 
  作者：{{ document.author }}  
  来源：{{ document.domain }} 
  另外，在阅读此文章时，我对以下部分进行了高亮，认为这些是文章的重点，给你学习参考： 
  {% for highlight in document.highlights %} 
  - {{ highlight.content }} 
  {% endfor %} 
  {#- 下面的 if-else 逻辑检查文档的长度。如果文档较长，它将使用关键句子以避免超出 GPT 提示窗口的限制。我们强烈建议除非您知道自己在做什么，否则不要更改此设置。-#} 
  {% if (document.content | count_tokens) > 2000 %} 
  {{ document.content | central_sentences | join('\n\n') }} 
  {% else %} 
  {{ document.content }} 
  {% endif %} 
  """ 
  重要提示：请不要超过 500 字。每句话应该简洁易读；关于中文的排版原则：在中文和英文或数字之间，要有一个半角空白，例如：Apple 手机；3 个 AI 工具。 
  另外，在文章总结下方换行后，基于本文重点，请创建 3 个有关问题及其答案。每个问题应能帮助深入理解文章的关键概念，并加强对重点的印象。请注意，在问题的这一行上，使用粗体的 markdown 格式，或者 <strong> 的 html 标签来更让文字显眼。 
  {#- 创建问答 -#} 
  **问题 1：{# 基于重点内容构建的问题 #}** 
  答案：{# 对应问题 1 的答案，应该包含对应的重点内容 #} 
  **问题 2：{# 同上 #}**  
  答案：{# 对应问题 2 的答案，应该包含对应的重点内容 #} 
  **问题 3：{# 同上 #}** 
  答案：{# 对应问题 3 的答案，应该包含对应的重点内容 #} ([View Highlight](https://read.readwise.io/read/01j4b51px9h9pkdn4rhwxwrrzm))
- Tidy Tabs ([View Highlight](https://read.readwise.io/read/01j4b5h46d8zx16cch5ch5ec2g))
- 当开启了的页签数达到 6 个或以上时，上面会出现一个小扫帚的图标，点击这个图标，Arc 会根据网站的类型进行分类。 ([View Highlight](https://read.readwise.io/read/01j4b5h71paw1nvb1bc5jvcxy3))
- 下面是分类后的效果： ([View Highlight](https://read.readwise.io/read/01j4b5hq6gb5rx1z75jx96sy1t))
- ![[Attachments/39c32445aa56bf296318ab924a6b7273_MD5.webp]]([View Highlight](https://read.readwise.io/read/01j4b5hmc6yrrrre1kkva6qqhs))
- Instant Links ([View Highlight](https://read.readwise.io/read/01j4b5hx4yhfbeb2w3mwwgerk5))
- 这个功能很有用，在 Arc 浏览器中搜索时（输入网页地址的框），直接按 Shift + Enter ，Arc 会根据输入的内容智能找到一个唯一结果页面并且打开，省掉了在搜索引擎中手动去过滤结果的步骤。 ([View Highlight](https://read.readwise.io/read/01j4b5hzz0wfwbjnw29x1pxses))
- 比如：搜索「dotnet8下载页面」或者「冯威博客」，按下  Shift + Enter ，会出现下图的提示文本：Searching the web for you... ，稍等一下，就会以新页签打开搜索结果。 ([View Highlight](https://read.readwise.io/read/01j4b5j4me56402bxbj2pgazt4))
- ![[Attachments/4c88d846d00d4e17914d59b70785f199_MD5.webp]]([View Highlight](https://read.readwise.io/read/01j4b5jagsmzcbd3k1bxf9f2y6))
- Ask on Page ([View Highlight](https://read.readwise.io/read/01j4b5jes7kfwfata033jbr851))
- 我们使用 Arc 浏览网页一些文章时可以使用 Command+F 对网页进行提问： ([View Highlight](https://read.readwise.io/read/01j4b5jhrgnkn2hvsv29k8x27t))
- ![[Attachments/e1942775d723d4cbda6df052c4348d9a_MD5.webp]]([View Highlight](https://read.readwise.io/read/01j4b5jmymmxbcvr1dtegbtqfn))
- 在回答中可以点击 Find on Page 找到原文的参考文本： ([View Highlight](https://read.readwise.io/read/01j4b5k0ep2w80gb5h89zdangn))
- ![[Attachments/a6d18314ba76d26981a8247a57f9cf34_MD5.webp]]([View Highlight](https://read.readwise.io/read/01j4b5jwpamtqr3hwzfk6bvnbp))
- 这个功能可以大大提升浏览网页的效率。 ([View Highlight](https://read.readwise.io/read/01j4b5k47h31cw512s4t5qzsyj))
- 5-Second Previews ([View Highlight](https://read.readwise.io/read/01j4b5k8yt6qe64zpt2s82k4xr))
- 在 google 中进行搜索时，鼠标悬停到搜索结果的链接上，会自动总结这个链接的内容，以卡片的形式进行展示，不用在一个一个链接点击去看了： ([View Highlight](https://read.readwise.io/read/01j4b5kf3j4kgwqt8hpd1ncrjb))
- ![[Attachments/afb3412d9c1fef0667db4d5deed64e6a_MD5.webp]]([View Highlight](https://read.readwise.io/read/01j4b5kmfzz7hr193ktpabma3n))
- Tidy Tab Titles ([View Highlight](https://read.readwise.io/read/01j4b5kq5f12g21vj9agh1sehb))
- 当把页签从临时区拖到固定区时，会自动对页签的标题进行重命名，如果不满意也可以双击进行修改，下面是 .NET 下载页面的标题前后对比： ([View Highlight](https://read.readwise.io/read/01j4b5kwhzdezjd51c6s46gzf1))
- Tidy Downloads ([View Highlight](https://read.readwise.io/read/01j4b5m4p6g3btsejcm6a92968))
- 在 Arc 浏览器中进行下载时，会自动重命名下载的文件名，让文件名更容易理解： ([View Highlight](https://read.readwise.io/read/01j4b5m7n2sybqpgrgdssq6zrr))
- ChatGPT in the Command Bar ([View Highlight](https://read.readwise.io/read/01j4b5mbyadyxjzvzzmbfyqbx6))
- 在地址栏输入「ChatGPT」，然后点击 Tab 键，会出现如下界面： ([View Highlight](https://read.readwise.io/read/01j4b5mep5a286m8x1ye71wtrc))
- ![[Attachments/3df658bcebacfcce0b064ae4a6a29252_MD5.webp]]([View Highlight](https://read.readwise.io/read/01j4b5mkvk2ag2pcz6qdm0aztc))
- 输入问题：介绍下 vue 的基本使用，Arc 会进入到 ChatGPT 的回答页面： ([View Highlight](https://read.readwise.io/read/01j4b5mp7k6a2x9wj7bvajbp7s))
- ![[Attachments/c5c001724bf3e259e59c44e986cb3acf_MD5.webp]]([View Highlight](https://read.readwise.io/read/01j4b5ms74fwgfpszn4mq046w1))
