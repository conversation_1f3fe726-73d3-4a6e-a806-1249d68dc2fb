---
人员: 
  - "[[火箭君]]"
tags:
  - articles
日期: 2024-08-17
时间: 2024-08-19 02:49:14.721128+00:00
相关:
  - "[[AI]]"
  - "[[AI工具]]"
  - "[[Capacities]]"
  - "[[DeskOrg Project]]"
  - "[[DropIt]]"
  - "[[Every]]"
  - "[[Figma]]"
  - "[[<PERSON> Juggler]]"
  - "[[Folder Tidy]]"
  - "[[freshen]]"
  - "[[GPT]]"
  - "[[macOS]]"
  - "[[OpenAI]]"
  - "[[P.A.R.A. 分类法]]"
  - "[[Sparkle]]"
  - "[[yml]]"
  - "[[上传]]"
  - "[[下载]]"
  - "[[云端相册]]"
  - "[[云笔记]]"
  - "[[人工智能]]"
  - "[[分类器]]"
  - "[[切分]]"
  - "[[压缩]]"
  - "[[打印]]"
  - "[[文件分类]]"
  - "[[文件名]]"
  - "[[文件夹]]"
  - "[[文件改名]]"
  - "[[文件整理]]"
  - "[[文件移动]]"
  - "[[文件管理]]"
  - "[[文档]]"
  - "[[服务器]]"
  - "[[桌面]]"
  - "[[生产力]]"
  - "[[电子发票]]"
  - "[[科技]]"
  - "[[笔记分类]]"
  - "[[自动化]]"

链接: https://mp.weixin.qq.com/s/ipou3ELDXeBre1sqpLGVFw
附件: https://mmbiz.qpic.cn/mmbiz_jpg/hQibibdG339M1G8ib1rRbdg9EL7ArubR1eJ1PRHECB0icv8sISEjWxWCqEL1VibfyBnvz4ziaaFX1AZt1SKRKuTKVOcQ/0?wx_fmt=jpeg)
---
## Document Note

## Summary

数字信息很多时候会以「文件」的形式「沉淀」下来。文件堆放的重灾区是……

## Full Document
[[Full Document Contents/Articles/火箭君-现在已经要用 Ai 来整理杂乱的桌面文件了？（1款ai工具 + 4 款经典工具）.md|See full document content →]]

## Highlights
- 今天我想先简单介绍一下海外某新款实验室性质的 AI 文件整理 App， 然后，我会让大家比较一下几款经典的早期整理工具（直到今天还很有市场）， 以及我个人的一些实践方法。
  Sparkle by Every
  Sparkle 是一款基于 AI 的文件分类的工具，目前仅支持 macOS。Sparkle 由 Every 提供。Every 不是那个文件搜索工具 Everything， 而是一个知名的时事通讯网站（我个人很喜欢）。
  Sparkle 可以帮助我们整理 Mac 上最常用的三个文件夹：桌面、文档 和 下载。
  当我们设置 Sparkle 时，可以指定一些文件夹让 AI 处理，AI 会帮我们保持这些文件夹干净；同样也可以选择「例外」文件夹，让 App 不要去乱动。
  Sparkle 会将文件分类到三个大类别子文件夹：
  • 最近的库 （Recents）
  • AI库（AI Library）
  • 手动库 （Manual Library）
  ![[Attachments/199088d411a7955d5beb8a88af9c2e6c_MD5.webp]]
  最近库：任何少于三天的文件都放在这里，以方便访问。
  AI库：任何*超过三天的文件*都放在这里。Sparkle 会自动为它在您电脑上找到的文件创建一个自定义子文件夹结构。新文件进入时将自动排序到该子文件夹结构中。子文件夹结构会根据 AI 判断进行分类（下图），效果就见仁见智了。
  ![[Attachments/d36c4a1173824cf3fd2d32ad38ad0c64_MD5.webp]]
  手动库： 如果你想手动维护文件夹结构，可以将选定的文件和文件夹放入手动库，Sparkle 就不会去动它们。
  这三个文件夹能让所有文件至少看起来干净整洁，通过文件夹结构，让我们在需要的时候相对轻松地找到所需的东西。
  Sparkle 自称使用 OpenAI 的 GPT 来组织您的文件。为此，文件名会被发送到 Every 的服务器，然后再发送到 OpenAI 。Every 还号称只会暂时存储文件名，并在30天后删除它们，以确保用户隐私安全。 ([View Highlight](https://read.readwise.io/read/01j5m8kt9nh9szc4wp39mz2nsm))
