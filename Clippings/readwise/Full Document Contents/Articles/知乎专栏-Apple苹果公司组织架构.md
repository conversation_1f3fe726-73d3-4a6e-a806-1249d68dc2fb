---
人员: 
  - "[[知乎专栏]]"
tags:
  - articles
日期: 2021-08-16
时间: None
链接: https://zhuanlan.zhihu.com/p/400510124
附件: https://static.zhihu.com/heifetz/assets/apple-touch-icon-152.a53ae37b.png)
---
## Document Note

## Summary

Apple苹果公司组织架构  这种模式，自乔布斯回到苹果时，开始实施，一直保留到现在。 今天，苹果大学校长兼副总裁 Joel Podolny 在《哈佛商业评论》上发表了一篇题为《How Apple is Organized for Innovation》的…

## Full Document
**Apple苹果公司组织架构** 

![[Attachments/70fe100a9b05158a17cbcfa93ef3043d_MD5.jpg]]
这种模式，自乔布斯回到苹果时，开始实施，一直保留到现在。

今天，苹果大学校长兼副总裁 Joel Podolny 在《哈佛商业评论》上发表了一篇题为《How Apple is Organized for Innovation》的文章，深度解析了苹果的组织架构以及这种架构如何帮助苹果保持创新与发展。

文中写道，苹果以其在硬件、软件和服务方面的创新而闻名。然而**，不为人所知的是，在引领苹果创新并取得成功过程中，起到关键作用的组织设计和相关的领导模式。**

乔布斯回归与新组织架构形成

**这种模式，自乔布斯回到苹果时开始实施，一直保留到现在。**

1997 年，当史蒂夫·乔布斯重返苹果时，苹果的规模和范围都是传统的结构。划分为业务部门，每个部门都有损益责任。重新掌舵后，乔布斯将整个公司置于一个损益表之下，并将各业务的不同部门，合并为一个功能性组织，将专业知识与决策权结合起来，这是苹果至今仍保留的结构。

两个组织结构图显示，从 1998 年到 2019 年苹果的结构是如何变化的。

![[Attachments/bdd9cb7799e530ace2e3a59b27ffdea6_MD5.jpg]]
1998 年，向首席执行官报告的业务部门，包括硬件、软件、市场营销、运营、服务和支持、销售、财务和法律。

2019 年，当整个公司处于一个损益表下时，向 CEO 报告的部门包括设计、硬件工程、硬件技术、软件、服务、机器学习和人工智能、市场营销、营销通信、运营、销售、零售、人员、财务、法律、企业通信、环境、政策和社会，以及企业发展。

对于当时苹果这样规模的公司来说，采用功能结构可能并不奇怪。**令人惊讶的是，尽管现在苹果公司的收入是 1998 年的近 40 倍，复杂程度也远高于 1998 年，但苹果公司仍保留了这个结构。**

高级副总裁负责职能，而不是产品。与乔布斯之前的情况一样，**现在首席执行官蒂姆•库克（Tim Cook）在组织结构图上占据着唯一一个职位，在这个职位上，苹果公司的任何主要产品的设计、工程、运营、营销和零售都有交集。**实际上，除了首席执行官之外，该公司没有常规的总经理：控制着从产品开发到销售的整个过程，并根据损益表进行判断。

正如哈佛商学院历史学家阿尔弗雷德·钱德勒（Alfred Chandler）所记载的那样，杜邦（DuPont）和通用汽车（General Motors）等美国公司，在 20 世纪初从职能结构转变为多部门结构。

到本世纪后半叶，绝大多数大公司都效仿了这一做法。苹果公司证明，**功能结构可能会使面临巨大技术变革和行业动荡的公司受益。**

苹果对功能性组织的承诺，并不意味着结构保持不变。随着人工智能和其它新领域重要性的增加，这种结构也发生了变化。在这里，将讨论苹果独特且不断发展的组织模式，带来的创新益处和领导力挑战，这对于那些希望更好地理解，如何在快速变化的环境中，取得成功的个人和公司，可能很有用。

为什么是职能组织？

苹果的主要目的是创造丰富人们日常生活的产品。这不仅包括开发全新的产品类别，如iPhone 和Apple Watch，还包括在这些类别中不断创新。也许没有哪款产品的功能能，比iPhone 相机更能体现苹果对持续创新的承诺。

2007年，当 iPhone 被推出时，史蒂夫•乔布斯（stevejobs）在一年一度的新品发布会上，只花了 6 秒钟的时间，对着 iPhone 相机。从那时起，iPhone 相机技术为摄影行业，做出了一系列创新：高动态范围成像（2010 年）、全景照片（2012 年）、真色调闪光灯（2013 年）、光学图像稳定（2015 年）、双镜头相机（2016 年）、肖像模式（2016 年）、肖像照明（2017 年）和夜间模式（2019 年），这只是其中的一小部分改进。

**苹果的领导者需要深厚的专业知识、对细节的专注，以及合作性的辩论。**

为了创造这样的创新，苹果依赖于**一个以功能专长为中心的结构。**基本信念是，那些在某个领域拥有最多专业知识和经验的人，应该拥有该领域的决策权。

这是基于两种观点：

**首先，苹果在技术变革和颠覆率较高的市场竞争，因此，必须依靠对造成破坏的技术，有深入了解的人的判断和直觉。**在获得市场反馈和可靠的市场预测之前，该公司必须对哪些技术和设计可能在智能手机、电脑等领域取得成功进行押注。依靠技术专家，而不是总经理会增加这些赌注得到回报的几率。

**其次，如果短期利润和成本目标是判断投资和领导者的首要标准，那么，苹果提供最好产品的承诺将被削弱。**值得注意的是，高级研发主管的奖金是基于整个公司的业绩数字，而不是特定产品的成本或收入。因此，产品决策在某种程度上与短期财务压力无关。财务团队不参与工程团队的产品路线图会议，工程团队不参与定价决策。

并不是说苹果在决定公司将采用哪些技术和功能时，不考虑成本和收入目标。但在方式上不同于那些传统组织的公司。 **研发领导者不应将总体成本和价格目标，作为进行设计和工程选择的固定参数，而是要权衡这些选择给用户带来的好处和成本考虑。**

![[Attachments/b91a1a36320c7ef89fa20b343799969e_MD5.jpg]]
在一个功能性的组织中，个人和团队的声誉是下注的控制机制。一个很好的例子是，在 2016 年，iPhone7 Plus 决定推出带有肖像模式的双镜头相机。**这是一个很大的赌注，即相机对用户的影响，将足以证明其巨大的成本是合理的。**

一位高管告诉，保罗•胡贝尔（Paul Hubel）是一位在肖像模式工作中扮演核心角色的高级领导人，「正在滑雪」，这意味着，团队正在冒着很大的风险：如果用户不愿意为一部价格更高、质量更好的手机支付额外费用，那么，该团队下次很可能会失去信誉，提出一个昂贵的升级或功能。**事实证明，这款相机是 iPhone7 Plus 的一项重要功能，成功进一步提高了 Hubel 和团队的声誉**。

当决策者是那些在各自领域拥有深厚专业知识的领导者，而不是总经理主要负责实现数字目标时，更容易在关注成本和增加用户体验价值之间取得平衡。**传统业务部门结构的基本原则，协调责任和控制，而职能组织的基本原则，协调专业知识和决策权。**

因此，苹果的组织方式和所产生的创新类型之间的联系，显而易见的。正如钱德勒（Chandler）的名言，「结构遵循战略」——尽管苹果没有采用预期的大型跨国公司会采用的结构。

现在看看苹果公司架构背后的领导模式。

三个领导特征

自从史蒂夫·乔布斯（Steve Jobs）实施职能组织以来，苹果公司的各级管理人员，从高级副总裁到下，都被期望拥有三个关键的领导特征：

1、深厚的专业知识，能够有意义地参与到各自职能范围内的所有工作中；

2、专注于这些职能的细节；

3、愿意在集体决策过程中合作讨论其它职能。

当管理者拥有这些属性时，决策是由最有资格做出决策的人，协调的方式做出的。

**1、深厚的专业知识**

苹果不是一家总经理监督经理的公司，而是一家由专家领导专家的公司。**人们的假设是，培养一个管理专家，要比把管理者培养成专家容易得多**。在苹果，硬件专家管理硬件，软件专家管理软件（偏离这一原则的情况很少见）。这种方法通过不断增加的专业化领域，在组织的各个层面上进行级联。

苹果的领导者们相信，世界级的人才希望在某个专业领域与其它世界级人才共事。这就像加入一个运动队，在那里，可以学习和发挥最好的。

很早以前，史蒂夫·乔布斯就开始接受这样一个观点：

**苹果公司的经理们，应该是管理领域的专家。**在 1984 年的一次采访中说：「在苹果经历了那个阶段，当时出去想，要成为一家大公司，让雇佣专业的管理人员。出去雇了一群专业的管理人员。一点也不管用……知道如何管理，但在专业方面什么都不知道。如果是一个伟大的人，为什么想为一个你什么都学不到的人工作？知道什么是有趣的吗？知道谁是最好的经理吗？伟大的个人贡献者，从来都不想成为一名管理者，但却决定必须成为，因为，没有其他人能够出色地完成工作。」

一个例子是 Roger Rosner，苹果软件应用业务的负责人，该业务包括工作效率应用程序，如 Pages（文字处理）、Numbers（电子表格）、Keynote（演示文稿），以及 GarageBand（音乐合成）、iMovie（电影编辑）和 News（提供新闻内容的应用程序）。

罗斯纳曾在卡内基梅隆大学（Carnegie Mellon）学习电子工程，2001 年加入苹果，担任高级工程经理，升任 iWork 应用主管、生产力应用副总裁，2013 年起担任应用副总裁。罗斯纳在几家较小的软件公司，担任工程总监期间，积累了丰富的专业知识，一位专家级领导专家的典范。

**在一个功能性组织中，专家领导专家，意味着，专家们在某一特定领域，建立一个很深的长凳，在那里可以相互学习。**例如，苹果公司 600 多名相机硬件技术专家，组成了一个由相机专家格雷厄姆汤森（Graham Townsend）领导的团队。因为 iPhone、iPad、笔记本电脑和台式电脑都包括摄像头，所以，如果苹果按业务部门进行组织，这些专家将分散在各个产品线中。这将削弱集体专长，降低解决问题、产生和改进创新的能力。

**2、沉浸在细节中**

贯穿苹果公司的一个原则，「领导者应该知道组织的三个层次的细节」，这对于在最高层迅速有效的跨职能决策，至关重要。如果经理出席决策会议时，没有掌握细节，则必须在没有细节的情况下做出决策，或者推迟决策。

经理们会讲一些战争故事，告诉那些深入研究电子表格、代码行或产品测试结果的高级领导。

当然，许多公司的领导人坚持说，团队都深谙细节。**但很少有机构能与苹果媲美，**高层领导是如何对产品圆角的确切形状，给予极大关注的。

圆角的标准方法，使用圆的圆弧连接矩形对象的垂直边，这会产生从直线到曲线的某种突然过渡。相比之下，苹果的领导者们，坚持连续的曲线，从而形成了一种在设计界被称为「迂回」的形状：坡度开始得更快，但不那么突然。没有曲率突变的硬件产品的一个优点，可以产生更柔和的高光（即沿拐角的光反射几乎没有跳跃）。

两者之间的差别是微妙的，对执行并不仅仅是一个更复杂的数学公式的问题。要求苹果公司的运营领导者，致力于极为精确的制造公差，以生产数以百万计的 iPhone 和其它带圆括号的产品。这种对细节的深入研究，不仅仅是一个被推到下级的问题，而是在领导层的核心。

![[Attachments/8336dedb44abc9b318ae834138eb8336_MD5.jpg]]
拥有在各自领域的专家，能够深入细节的领导者，对苹果的运营方式，有着深远的影响。领导者可以推动、调查和「嗅到」一个问题。知道哪些细节是重要的，以及将注意力集中在哪里。**苹果公司的许多人认为，为专家工作是一种解放，甚至是令人振奋的工作方式，**因为，专家比总经理提供更好的指导和指导。在一起，所有人都可以努力在所选择的领域，做生命中最好的工作。

**3、愿意合作辩论**

苹果公司在整个公司有数百个专家团队，其中几十个团队可能需要一个新产品的关键组成部分。例如，带有人像模式的双镜头相机，需要不少于 40 个专业团队的合作：硅设计、相机软件、可靠性工程、运动传感器硬件、视频工程、核心运动和相机传感器设计等等。

苹果究竟是如何开发和传递，需要这种协调的产品的？

**答案是合作辩论。**

因为没有一个职能部门单独负责产品或服务，跨职能协作至关重要。当辩论陷入僵局时（有些人不可避免地会这样做），高层管理者会作为决断者参与进来，有时包括首席执行官和高级副总裁。即便是最优秀的领导者，要想迅速做到这一点，并对细节给予足够的关注，也是一项挑战，这就使得公司让拥有苹果运营经验的副总裁中，担任许多高级职位，变得更加重要。

然而，鉴于苹果目前的规模，即使是高管团队，也只能解决有限数量的僵局。大量的横向依赖，意味着副总裁和主管级别的无效同级关系，这不仅有可能破坏特定的项目，而且会破坏整个公司。**因此，要想在一项职能中获得并保持领导地位，必须是高效的合作者。**

这并不意味着，人们不能表达观点。人们期望领导人，持有强有力的、有根据的观点，并大力支持这些观点，但当有证据表明，其他人的观点更好时，也愿意改变主意。

当然，这样做并不总是容易的。一个领导者，既有党派性又有开放性的能力，由两件事促成的：

**一是对公司价值观和共同目标的深刻理解和忠诚；**

**二是致力于将一条特定道路的正确性与难易程度区分开来，以使执行决策的难度不妨碍被选中。**

iPhone 肖像模式的发展说明了领导层对细节的狂热关注、团队间的激烈辩论，以及共同目标，对解决辩论形成最终结果的强大影响力。

2009 年，Hubel 想到了开发一个 iPhone 功能，让人们可以用 bokeh 拍摄肖像照片——这是一个日语术语，指的是令人愉悦的模糊背景，摄影专家通常认为这是最高质量的。

当时只有昂贵的单镜头反光相机，可以拍摄这样的照片，但是 Hubel 认为，通过双镜头设计和先进的计算摄影技术，苹果可以在 iPhone 中增加这种功能。想法很好地符合摄影团队的既定目标：「更多的人用更多的时间，拍摄更好的图像。」

当团队努力将这个想法变为现实时，出现了几个挑战。最初的尝试产生了一些惊人的肖像图片，但也出现了一些「失败案例」，在这些案例中，算法无法区分清晰浮雕中的中心对象（例如脸部）和模糊的背景。例如，如果要从铁丝网后面拍摄一个人的脸，就不可能构造一种算法，以与前面的铁丝网一样的锐度，穿透铁丝网，捕捉到脸的侧面。

![[Attachments/f05f4c106fbe14be0a2b43dccf4ff1c8_MD5.jpg]]
有人可能会说，「谁在乎个案？但对于团队来说，避开工程师所称的「角落案例」的罕见或极端情况，将违反苹果严格的零工件工程标准，意味着「由相关技术和/或技术在数字处理过程中引入的，任何不希望的，或意外的数据更改。」

负责固件和算法的传感器软件和用户体验原型制作副总裁 Myra Haggerty 回忆道，这个案例引发了相机团队和其它相关团队之间「许多艰难的讨论」。相机软件团队，最终向汇报的副总裁塞巴斯蒂安•马里诺•梅斯（Sebastien Marineau Mes）决定推迟到明年发布，以便让团队有时间更好地解决故障案例—— **「这是一颗难以下咽的药丸」。**

为了在质量标准上达成一致，工程团队邀请了资深的设计和营销负责人会面，认为会提供一个新的视角。设计领袖们在这场辩论中增加了一种艺术敏感性，问道：「什么造就了一幅美丽的肖像画？「为了帮助重新评估零文物标准，从伟大的肖像摄影师那里收集图像。注意到，这些照片通常在脸部边缘模糊，但眼睛锐利。所以要求算法团队达到同样的效果。当团队成功时，知道有一个可以接受的标准。

出现的另一个问题，预览背景模糊的肖像照片的能力。相机团队设计了这个功能，让用户只有在拍照后，才能看到在照片上的效果，但人机界面（HI）设计团队却推辞了，坚持说用户应该能够看到「实时预览」，并获得一些关于如何在拍照前进行调整的指导。

HI 团队的成员约翰尼·曼扎里（Johnnie Manzari）给摄影团队做了一个演示。**「当看到演示时，意识到这是需要做的。」**汤森告诉。相机硬件团队的成员不确定能做到这一点，但困难并不能成为一个可以接受的借口。

经过数月的工程设计努力，视频工程团队（负责控制传感器和摄像机操作的低级软件）找到了一种方法，合作得到了回报。**肖像模式是苹果 iPhone7 Plus 营销的核心。**事实证明，这是用户选择购买并乐于使用手机的主要原因。

![[Attachments/f8551f43a41b6a9f7e7c80a46c69e2c7_MD5.jpg]]
正如这个例子所示，苹果的合作辩论涉及到来自不同职能部门的人，不同意、推倒、提倡或拒绝想法，并在彼此的想法基础上提出最佳解决方案。这需要高层领导保持开放的心态。还要求这些领导者激励或影响其它领域的同事，为实现目标做出贡献。

虽然汤森对相机的伟大负责，但需要几十个其它团队，每个团队都有承诺清单，以贡献时间和精力在肖像模式的项目。**在苹果公司，这被称为无控制的责任：即使不能控制所有其它团队，也要为项目的成功负责。**这个过程可能会很混乱，但会产生很好的结果。

当不同的团队以共同的目标工作时，就会出现「一团糟」，比如肖像模式项目。当团队把议程推到共同目标之前时，就会出现「糟糕的局面」。那些与糟糕的烂摊子联系在一起，不改变或不能改变行为的人，如果不主动从苹果公司辞职的话，将被免职。

不断扩张的规模领导

过去 20 年来，苹果的组织方式带来了巨大的创新和成功。然而，也并非没有挑战，尤其是自 2008 年以来，收入和员工人数激增带来了巨大的挑战。

随着公司的发展、进入新市场和进入新技术，其职能结构和领导模式，也必须不断演变。决定如何组织专业领域，以实现最佳协作和快速决策，一直是首席执行官的一项重要职责。

**蒂姆·库克近年来实施的调整，包括将硬件功能划分为硬件工程和硬件技术；增加人工智能和机器学习，作为一个功能领域；将人机界面移出软件，将其与工业设计相融合，创造出一个集成的设计功能。**

组织成长带来的**另一个挑战，给执行团队下面的几百名副总裁和董事带来的压力。**如果苹果要限制一位高级领导人的组织规模或范围，以限制该领导人预期拥有的细节的数量和广度，那么，该公司将需要大幅扩大高级领导人的数量，从而使这种行之有效的合作方式无法保持下去。

意识到这一问题，**苹果公司在限制高级职位的数量，以尽可能减少任何跨职能活动中必须涉及的领导人数方面，一直很有纪律。**2006 年，也就是 iPhone 发布的前一年，该公司拥有约 1.7 万名员工；到 2019 年，这一数字增长了 8 倍多，达到 13.7 万人。与此同时，VP 的数量大约翻了一番，从 50 个增加到 96 个。不可避免的结果是，高级领导人领导着更大、更多元化的专家团队，这意味着需要监督更多细节，以及超出其核心专业知识范围的新职责领域。

作为回应，在过去五年左右的时间里，许多苹果公司的管理者，一直在发展上述的领导方式：**专家领导专家、专注于细节、合作辩论**。已经将这些调整编入了称之为自由裁量的领导模式中，并将其纳入了一项针对苹果副总裁和董事的新的教育计划。目的是应对挑战，使这种领导方式，在公司所有领域推动创新，而不仅仅是产品开发。

在苹果公司规模较小的时候，期望领导者成为公司内部几乎所有事情的专家，并专注于其中的细节，这也许是合理的。然而，现在需要在时间和努力的地点和方式上，行使更大的自由裁量权。

必须决定哪些活动需要全神贯注于细节，因为这些活动为苹果创造了最大的价值。**其中一些将属于现有的核心专业知识（仍然需要拥有的），还有一些将要求学习新的专业领域。**需要领导者较少关注的活动，可以被推到其他人身上（领导者要么教别人，要么在不是专家的情况下授权）。

Rosner，应用程序副总裁，提供了一个很好的例子。和其他许多苹果经理一样，不得不应对苹果公司巨大增长，带来的三大挑战。

首先，在过去的十年里，无论是在员工总数（从 150 人到大约 1000 人）和在任何特定时间正在进行的项目数量，**职能规模都呈爆炸式增长。**显然，无法深入研究所有这些项目的所有细节。

第二，**业务范围扩大了**：在过去的 10 年里，负责开发新的应用程序，包括新闻、剪辑（视频编辑）、书籍和 Final Cut Pro（高级视频编辑）。虽然应用程序是核心专业领域，但其中的一些方面，包括新闻编辑内容、图书出版工作方式和视频编辑，都涉及到罗斯纳并非专家的问题。

最后，随着苹果的产品组合和项目数量的扩大，需要与其它职能部门进行更多的协调，从而增加了**跨多个部门协作的复杂性**。例如，罗斯纳负责新闻的工程方面，而其他经理则负责监督新闻依赖的操作系统、内容以及与内容创造者（如《纽约时报》）和广告商的业务关系。

为了应付，罗斯纳调整了自己的角色。作为一名领导其它专家的专家，一直专注于细节，特别是那些涉及软件应用程序的顶层方面及其架构的细节，这些方面影响到用户如何使用软件。还与整个公司的经理合作，参与涉及这些领域的项目。

但随着职责的扩大，已经把一些东西从自己的盒子里搬了出来，包括一些传统的生产力应用程序，比如 Keynote 和 Pages。**现在，指导其它团队成员并给予反馈，以便能够根据苹果的规范开发软件应用程序。**作为一名教师并不意味着罗斯纳在白板上进行指导；相反，对团队的工作提出了强烈的、通常是充满激情的批评（显然，没有核心专业知识的总经理，很难传授不知道的东西）。

罗杰·罗斯纳的自由裁量式领导权。

苹果应用副总裁罗杰·罗斯纳（Roger Rosner）负责管理一个由四个不同类别组成的投资组合，这些类别需要不同的时间和对细节的关注。

2019 年的情况是这样的：一张图表根据罗斯纳的专业程度（x 轴）和对细节的参与程度（y 轴）来排列这四个类别。

40% 的时间都花在「拥有」的盒子里（那里的专业知识和对细节的参与度最高），里面包含了苹果新闻、用户界面设计和软件架构的部分内容。「学习」框（30% 的时间，低专业知识，高细节）包含部分苹果新闻、语音备忘录和天气预报。「授权」框（15% 的时间，低专业知识，低细节）包含 iMovie、Final Cut Pro 和 GarageBand。「教学」框（他 15% 的时间，高专业知识，低细节）包含主题、页面和数字。

![[Attachments/c8d739a76ffd670d984d1e342236a906_MD5.jpg]]
罗斯纳面临的第二个挑战，在原来的专业知识之外，增加一些活动。六年前，被指派负责新闻的工程和设计。因此，必须学习如何通过应用程序发布新闻内容，以了解新闻出版物、数字广告、机器学习个性化新闻内容、隐私架构以及如何激励出版商。

因此，一些作品落入了学习的盒子里。**在这种情况下，管理者在获得新技能方面面临着陡峭的学习曲线。**考虑到这一要求有多高，只有关键的新活动才应该属于这一类。经过六年的紧张学习，罗斯纳已经掌握了其中的一些领域，这些领域现在都在他自己的盒子里。

只要一项特定的活动仍在学习箱中，领导者就必须采取初学者的心态，以一种暗示不知道答案（因为不知道）的方式询问下属。这与领导者就拥有和教学箱中的活动向下属提问的方式截然不同。

最后，罗斯纳已经将一些领域，包括 iMovie 和 GarageBand，并不是一个有必要能力的人的专家。对于授权箱中的活动，召集团队，就目标达成一致，监督和审查进度，并让团队负责：一般管理人员。

**而苹果的副总裁们，大部分时间都花在拥有和学习的盒子里，而其他公司的总经理们则把大部分时间花在授权箱里。**罗斯纳估计，将 40% 的时间花在拥有的活动上（包括与某一领域的其它人合作），30% 用于学习，15% 用于教学，15% 用于授权。当然，这些数字因经理而异，这取决于业务和特定时间的需求。

**自由裁量领导模式，保留了一个有效的功能性组织，在规模上协调专业知识和决策权的基本原则。**当像罗斯纳这样的领导者，在原来的专业知识之外，承担新的责任时，苹果可以有效地进入新的领域，当领导者传授手艺和委派工作时，团队的规模也可以扩大。相信，通过这样的组织方式，苹果将继续创新和繁荣。

苹果的功能性组织在非常大的公司中，即使不是独一无二的，也是罕见的。与流行的管理理论背道而驰，即公司在变大后应重组为部门和业务单位。但在向业务部门转移的过程中，失去了一些至关重要的东西：决策权与专业知识的一致性。

**为什么公司总是坚持让总经理负责业务部门？认为，其中一个原因是，做出改变很困难。**需要克服惰性，在管理者之间重新分配权力，改变以个人为导向的激励体系，以及学习新的合作方式。当一家公司已经面临巨大的外部挑战时，这是令人望而生畏的。一个中间步骤可能是培养专家领导的专家模型，即使是在一个业务单位结构内。

例如，在担任下一个高级管理职位时，要挑选在该领域具有深厚专业知识的人，而不是可能成为最好的总经理的人。但是，一个全面的转变，要求领导者也向一个功能性的组织转型。苹果公司的业绩证明，这些回报或许可以证明这些风险是合理的。方法可以产生非凡的结果。

本文的一个版本刊登在 2020 年 11 月至 12 月的《哈佛商业评论》（Harvard Business Review）上。

作者介绍：

乔尔波多尔尼（Joel M. Podolny）：加州库比蒂诺苹果大学的院长和副校长，波多尔尼曾任耶鲁大学管理学院院长，曾任哈佛商学院和斯坦福大学商学院教授。

莫滕·T·汉森 (Morten T. Hansen)：加州大学伯克利分校的教授，也是苹果大学的教员。《伟大的工作与协作》一书的作者，也是《伟大的选择》的合著者。2019 年，被 Thinkers50 评为世界顶级管理思想家之一。

参考链接：[https://www.sohu.com/a/427429402\_413980](https://link.zhihu.com/?target=https%3A//www.sohu.com/a/427429402_413980)
