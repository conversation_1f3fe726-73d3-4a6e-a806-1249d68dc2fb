---
人员: 
  - "[[张晓辉]]"
tags:
  - articles
日期: 2024-11-09
时间: None
链接: https://atbug.com/enhance-obsidian-experience-plugins-tips/
附件: https://atbug.com/../images/favicon_hu5b5b76fc3c003dd4877d20aec85213e5_1374_180x0_resize_box_2.png)
---
## Document Note

## Summary

上个月老崔发了 用 Obsidian 有效应对日常工作，介绍了日常使用的几个插件，其中有几个也是我常用的。正好最近在新的环境中配置 Obsidian，特此记录下常用的插件和技巧。
笔记这件事持续 N 多年了，从下面的数据来看算是重度笔记用户了吧。从起初的纯文本格式到 Markdown，从单纯的记录到知识库的建立，笔记应用也换了好几款。Obsidian 是我当前最喜欢的笔记应用，没有之一。Obsidian 是我去年从使用了 6 年的 Mweb 转过来的，甚至还为其写了一款 插件。
插件 是 Obsidian 最吸引我的点，除了官方的核心插件外，有社区插件（截止本文发出，共有 1986 个社区插件）。
轻核心 + 可扩展，也是很多应用的设计模式，真的是可盐可甜，丰俭由人。Obsidian 真是做到了极致，连目录导航都是通过插件来提供的。
下面是我使用的所有社区插件：
Advanced Tables Advanced URI Automatically reveal active file Banners Better Word Cound Calendar Clear Unused Images Dataview Dynamic Table Of Contents Excalidraw Iconize Image Upload Toolkit Kanban Linter Local Images Plus Media Extended Mind Map Obsidian Tabs Pandoc Plugin Periodic Notes Readwise Official Recent Files Reveal Active File Button Style Settings Tasks Templater Tracker Vault Full Statistics 下面介绍下在几个场景中我重度使用的几款插件（排名不分先后）。

## Full Document
![[Attachments/3789be37e50e2c98e2ddff5a9eb73f1f_MD5.jpg]]
上个月老崔发了 [用 Obsidian 有效应对日常工作](https://blog.fleeto.us/post/obsidian-daily-howto/)，介绍了日常使用的几个插件，其中有几个也是我常用的。正好最近在新的环境中配置 Obsidian，特此记录下常用的插件和技巧。

笔记这件事持续 N 多年了，从下面的数据来看算是重度笔记用户了吧。从起初的纯文本格式到 Markdown，从单纯的记录到知识库的建立，笔记应用也换了好几款。[Obsidian](https://obsidian.md) 是我当前最喜欢的笔记应用，没有之一。Obsidian 是我去年从使用了 6 年的 [Mweb](https://www.mweb.im/) 转过来的，甚至还为其写了一款 [插件](https://obsidian.md/plugins?id=image-upload-toolkit)。

![[Attachments/b8dfc1761499ddec7ca5a67d704f6e63_MD5.png]]
[插件](https://obsidian.md/plugins?search=image%20tool) 是 Obsidian 最吸引我的点，除了官方的*核心插件*外，有*社区插件*（截止本文发出，共有 1986 个社区插件）。

轻核心 + 可扩展，也是很多应用的设计模式，真的是可盐可甜，丰俭由人。Obsidian 真是做到了极致，连目录导航都是通过插件来提供的。

下面是我使用的所有社区插件：

* Advanced Tables
* Advanced URI
* Automatically reveal active file
* Banners
* Better Word Cound
* Calendar
* Clear Unused Images
* Dataview
* Dynamic Table Of Contents
* Excalidraw
* Iconize
* Image Upload Toolkit
* Kanban
* Linter
* Local Images Plus
* Media Extended
* Mind Map
* Obsidian Tabs
* Pandoc Plugin
* Periodic Notes
* Readwise Official
* Recent Files
* Reveal Active File Button
* Style Settings
* Tasks
* Templater
* Tracker
* Vault Full Statistics

下面介绍下在几个场景中我重度使用的几款插件（**排名不分先后**）。

##### [笔记格式化：Linter](https://atbug.com/enhance-obsidian-experience-plugins-tips/#笔记格式化linter)

Linter 插件真是的省事省力省心，让强迫症的我写起笔记更轻松。Linter 可以自动格式化笔记，比如中英文间的空格；标题、段落、列表、代码块前后的空行等等，延缓空格、回车的寿命。

Linter 细化的配置特别多，能想到的基本都有。

![[Attachments/4509e63bdf758e63fdc904ec7e6ec5eb_MD5.png]]
##### [看板： Kanban](https://atbug.com/enhance-obsidian-experience-plugins-tips/#看板-kanban)

使用 Kanban 插件可以在 Obsidian 创建支持 Markdown 的 Kanban，我经常都是连接到其他笔记，方便进行规划。

![[Attachments/58d98ebf97aa4d31655a217905245434_MD5.png]]
##### [任务跟踪：Calendar + Dataview + Tasks + Templater](https://atbug.com/enhance-obsidian-experience-plugins-tips/#任务跟踪calendar--dataview--tasks--templater)

如果说看板是粗粒度的计划管理，那个这几个插件的组合就是更细粒度的任务追踪了。日常的生活、学习和工作可以通过任务的形式进行跟踪，比如设置截止日期、优先级等等。然后，把任务的规划变成每天、每周习惯，这一点我最近几个月有些懈怠了（原因暂不表，有机会再好好写写），最近慢慢恢复。

![[Attachments/7bf49b6244ae0d6416db96ac6fb4bc73_MD5.png]]
这是几个插件组合实现的：

* Calendar 可以展示每日完成和未完成的任务数，点击日期可以快速创建当天的日志。
* Tasks：可以跟踪整个笔记库中的任务；可以对每个任务进行标记（截止日期、完成日期、优先级等）；提供查询语法对任务进行过滤。
* Dataview：知识库的实时索引和查询引擎，提供了丰富的查询语法。日志中的阅读列表，就是用该插件索引 Readwise 上的高亮内容。
* Templater：每天的日志有固定的格式，比如跟踪任务，设置新任务等等。通过 Templater 设置模板，创建的时候自动套用模板。

有兴趣的可以参考 [我的模板](https://gist.github.com/addozhang/3f9a1bcf3606c3e2c81046e604dd6015)。

##### [图片上传：Image Upload Toolkit](https://atbug.com/enhance-obsidian-experience-plugins-tips/#图片上传image-upload-toolkit)

这是我写那个插件，用于平替 Mweb 中的功能：将笔记中的本地上传到图库中并自动替换图片的 web 地址后将笔记内容复制的剪切板（笔记中仍使用本地图片），方便发布到博客等平台。

插件目前支持的图床有 imageur、Aliyun Oss、ImageKit、Amazon S3、TencentCloud COS。

![[Attachments/17bd25ee2c82a47b07cc2f23c53bca2d_MD5.gif]]
##### [阅读碎片：Readwise Official](https://atbug.com/enhance-obsidian-experience-plugins-tips/#阅读碎片readwise-official)

[Readwise](https://readwise.io) 是一款帮助用户整理和复习阅读内容的工具，提供多种浏览器插件，可以对网页内容进行高亮标记。Readwise 自家的 Reader 阅读应用支持 Feed 订阅、内容高亮、稍后阅读等功能。

Readwise Official 作为 Readwise 官方提供的插件，可以将 Readwise 平台上高亮标记的内容同步到 Obsidian 中，作为知识库内容的一部分。

##### [视频笔记：Media Extended](https://atbug.com/enhance-obsidian-experience-plugins-tips/#视频笔记media-extended)

Media Extended 可以将视频无缝嵌入到笔记中，可以对视频进行截图、对时间戳进行笔记，方便记录视频中的重要部分，并记录下自己的想法。

当需要记录时间戳时，只需要点击 ⭐ 即可；点击笔记中的时间戳可以快速定位到对应的帧。

![[Attachments/456ae661032c7e3a9245404819df50aa_MD5.png]]
##### [美化：AnuPpuccin + Style Settings + Iconize](https://atbug.com/enhance-obsidian-experience-plugins-tips/#美化anuppuccin--style-settings--iconize)

一款好的笔记应用，除了功能外，颜值也是很重要的一部分。

[AnuPpuccin](https://github.com/AnubisNekhet/AnuPpuccin) 是 Obsidian 2022 Gem 的获奖主题，支持多种颜色方案和布局。AnuPpuccin 重度使用了 Style Settings 插件，通过该插件可以控制主题的大部分功能。

![[Attachments/01b65036527820cd3aa5643812e34baa_MD5.png]]
Iconize 插件则可以对文件、文件夹、标题，甚至是笔记的段落定义图标。

![[Attachments/6cb9ffb0b8048b95ce79799508f262c0_MD5.png]]
