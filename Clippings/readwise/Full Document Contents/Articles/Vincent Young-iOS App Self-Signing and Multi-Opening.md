---
人员: 
  - "[[<PERSON>]]"
tags:
  - articles
日期: 2023-09-29
时间: 2023-10-02 13:18:55.909021+00:00
链接: https://missuo.me/posts/sign-ios-app/
附件: https://readwise-assets.s3.amazonaws.com/static/images/article0.00998d930354.png)
---
## Document Note

## Summary

最近好像很多人对这个很感兴趣，我就简单介绍一下。
自签名有什么用 iOS 本身不支持多开，比如微信你只能同时登录一个，不像安卓。所以非常的不方便，自签名就可以实现双开，甚至是多开。
iOS 的 App 正常情况下只能通过 App Store 下载，有些好用的 App 价格又非常贵，而自签名可以直接安装破解版 App。比如 X(Twitter)、Instagram 这些去广告，微信防撤回等等。我就不一一列举了，能实现的功能非常多且强大。
简单说，就是多开和破解。
准备工作 签名的时候需要开发者证书，证书主要分为两大类，企业证书和个人证书，但是企业证书一般很难搞到，所以本文介绍的是个人证书。
获取个人证书之前，你需要知道你设备的 UDID，开发者需要将你的设备添加到测试设备中。每个开发者一共可以添加 100 台设备。你可以自行某宝或者找别的路子购买，售价大概在 40-100 不等一年。
购买证书需要提供 UDID，这个编号不会因为你刷机重装系统而改变。获取的方式，可以点 这个链接，需要安装一个描述文件。获取那一串字符串之后，记得保存一下。然后就可以删除这个工具的主屏幕图标和描述文件，之后不再需要用到。
证书 证书包含以下两个文件：
xxxx.p12 xxxx.mobileprovision 一般的证书签发的时间为一年。你买到的证书一般是不满一年的，因为不是实时生成的，这是很正常的。建议从靠谱的来源购买证书，因为如果一年之后到期，如果用同一个开发者的证书，可以直接覆盖安装，会保留 App 的数据，比如微信的聊天记录。如果使用不同开发者的证书无法覆盖安装，可能会丢失 App 数据。
签名 主流的签名工具，主要有 全能签 和 轻松签(Esign)，前者功能几乎一样，我个人一直用的 全能签。
当然啦，这是手机上的签名工具，你也可以用电脑签名，例如 iOS App Signer，功能没有手机端强大，例如移除应用跳转这些需要自己改动 .ipa 包。
安装全能签 请选择 “我有证书|自助安装”，上传 p12 和 mobileprovision 文件，输入 p12 证书的密码。
安装完之后可能会提示无法打开，会引导你到设置里面开启 开发者模式。如果你没有安装全能签，一般情况下从 iOS 16 开始你是看不到 开发者模式 这个选项的。开发者模式在 设置 - 隐私与安全性，划到最底下，你可以看到 锁定模式 和 开发者模式。开启开发者模式之后会...

## Full Document
> 最近好像很多人对这个很感兴趣，我就简单介绍一下。
> 
> 

iOS 本身不支持多开，比如微信你只能同时登录一个，不像安卓。所以非常的不方便，自签名就可以实现双开，甚至是多开。

iOS 的 App 正常情况下只能通过 App Store 下载，有些好用的 App 价格又非常贵，而自签名可以直接安装破解版 App。比如 X(Twitter)、Instagram 这些去广告，微信防撤回等等。我就不一一列举了，能实现的功能非常多且强大。

简单说，就是多开和破解。

签名的时候需要开发者证书，证书主要分为两大类，企业证书和个人证书，但是企业证书一般很难搞到，所以本文介绍的是个人证书。

获取个人证书之前，你需要知道你设备的 **UDID**，开发者需要将你的设备添加到测试设备中。每个开发者一共可以添加 100 台设备。你可以自行某宝或者找别的路子购买，售价大概在 40-100 不等一年。

购买证书需要提供 **UDID**，这个编号不会因为你刷机重装系统而改变。获取的方式，可以点 [这个链接](https://www.pgyer.com/tools/udid)，需要安装一个描述文件。获取那一串字符串之后，记得保存一下。然后就可以删除这个工具的主屏幕图标和描述文件，之后不再需要用到。

证书包含以下两个文件：

一般的证书签发的时间为一年。你买到的证书一般是不满一年的，因为不是实时生成的，这是很正常的。建议从靠谱的来源购买证书，因为如果一年之后到期，如果用同一个开发者的证书，可以直接覆盖安装，会保留 App 的数据，比如微信的聊天记录。如果使用不同开发者的证书无法覆盖安装，可能会丢失 App 数据。

主流的签名工具，主要有 **[全能签](https://udid.nuosike.cn/sign/)** 和 **[轻松签(Esign)](https://esign.yyyue.xyz/)**，前者功能几乎一样，我个人一直用的 **全能签**。

当然啦，这是手机上的签名工具，你也可以用电脑签名，例如 [iOS App Signer](https://dantheman827.github.io/ios-app-signer/)，功能没有手机端强大，例如移除应用跳转这些需要自己改动 .ipa 包。

请选择 “我有证书|自助安装”，上传 p12 和 mobileprovision 文件，输入 p12 证书的密码。

![[Attachments/af6912eb66934fa08bd337617b9c4a04_MD5.png]]
安装完之后可能会提示无法打开，会引导你到设置里面开启 **开发者模式**。如果你没有安装全能签，一般情况下从 iOS 16 开始你是看不到 **开发者模式** 这个选项的。开发者模式在 **设置 - 隐私与安全性**，划到最底下，你可以看到 **锁定模式** 和 **开发者模式**。开启开发者模式之后会重启手机，重启之后点击确认开启即可。

在使用全能签签名之前，你需要准备 ipa 文件，也就是 App 的包。请确保一定是砸壳包，否则无法签名。一般情况下你拿到的破解的或者多功能的包都是已经砸壳的。首先在全能签中导入你的 p12 证书，再导入 mobileprovision 文件，最后导入你需要签名的 ipa 文件。

点击你导入的包选择 **签名**，可以按照我这样设置，一般我会移除手表和插件，并且关闭应用跳转。另外，**移除已有库** 和 **添加第三方库** 是用于移除和注入 动态库(.dylib) 的，如果你不知道怎么操作，可以直接跳过，用别人已经注入好的包即可。

![[Attachments/9144e29532aa4f1bfb5d43994e7357d7_MD5.jpg]]
在签名时最关键的是 **Bundle Identifier**，下面主要分两种情况：

1. 你想要多开，也就是你想要同时使用官方的 App，即从 App Store 下载的版本，又要使用自签名的版本，那么你需要修改 **Bundle Identifier**。例如微信的 **Bundle ID** 你可以修改为 `me.missuo.xin`。如果你需要多开很多个微信，你分别取名为 `me.missuo.xin1`，`me.missuo.xin2`，`me.missuo.xin3` 等等。**如果你一旦修改了 Bundle ID，你的自签名的 App 一定是没有消息推送的。** APNs 服务器在推送消息的时候需要验证 **Bundle ID**，如果改过了就会推送失败。这个问题和证书没有任何关系。
2. 如果你想要消息推送很简单，仅需要卸载 App Store 的版本，在签名的时候不要改动包的 `Bundle ID`，你可以和官方版本一样正常的收到推送。

签完名之后，在 **已签名** 中点击你要安装的 App，点击安装即可。

软件源鱼龙混杂，有各种各样的，我这边就随便分享几个我自己在用的觉得还不错的分享 ipa 的 Telegram 频道和订阅源。

* [iOS破解软件分享](https://t.me/gekuGou)：破解的 App 会比较多。
* [登拜科技](https://t.me/dengbai)：更新的比较快，App 也不少。
* [苓妹妹ios资源分享](https://t.me/iosfulishare)：个人不常用，会注入频道的动态库，不太喜欢。
* [ios鸡神-Crack频道](https://t.me/iosapp520)：破解 App 也不少。
* [App Tester](https://repo.apptesters.org)：个人最喜欢的国外软件源，Twitter 之类的应用更新很快且纯净，且没有多余的注入。

**以上的软件源都是免费的且与本人没有任何的合作关系，如果你使用了这些软件源的包产生的任何后果自负。**

我目前在使用的自签名软件也不多：

* 微信：多开，也就是说我把 **Bundle ID** 改掉了，没有消息通知，但是我也不是很在意，多开上登录的是小号，平时找我的人不多。动态库都是我自己注入的，我也改动了一下主题。每个版本我都会自己注入并且会分享在 [GitHub](https://github.com/missuo/Inject-IPA/releases)。
* X(Twitter)：官替，卸载了官方版本，和官方一样正常消息推送，使用 App Tester 的包（注入了 BHTwitter），没有广告还有一些功能非常不错。
* Instagram：官替，卸载了官方版本，和官方一样正常消息推送，使用 App Tester 的包（注入了 Rocket 和 BHInsta），没有广告，没有建议的内容，可以随意批量下载照片和视频等等。
* NOMO RAW：官替，官方版本太贵了，我直接用的破解包。

以上软件我使用的包都是和 App Store 版本号一致的。因此不用担心新功能无法体验的问题。另外我使用的签名证书是我自己的开发者证书（花了99美元/年开通了 Apple Developer Plan）。

总之呢，自签名的玩法还是有很多的，比如注入动态库这些需要你自行研究，注入的教程你可以查看 [我的 GitHub](https://github.com/missuo/Inject-IPA)，有一些简单的介绍。除此之外，使用自签名的 App 可能会存在封号等问题，请谨慎使用，新注册的微信不建议使用。如果有什么别的疑问，欢迎在评论区讨论。
