---
人员: 
  - "[[36.AI]]"
tags:
  - articles
日期: 2025-06-29
时间: 2025-07-10 02:53:37.229329+00:00
相关:
  - "[[<PERSON> 4]]"
  - "[[Cline]]"
  - "[[Context 7]]"
  - "[[<PERSON><PERSON><PERSON>]]"
  - "[[Firecrawl]]"
  - "[[Gemini 2.5]]"
  - "[[GPT-4.1]]"
  - "[[LLM]]"
  - "[[Markdown]]"
  - "[[MCP Server]]"
  - "[[Perplexity]]"
  - "[[Puppeteer]]"
  - "[[Sequential Thinking]]"
  - "[[上下文]]"
  - "[[代码质量]]"
  - "[[克隆网站]]"
  - "[[商业模型]]"
  - "[[复杂问题]]"
  - "[[实时搜索]]"
  - "[[导航]]"
  - "[[库]]"
  - "[[开发者]]"
  - "[[批量抓取]]"
  - "[[推理]]"
  - "[[搜索]]"
  - "[[文档]]"
  - "[[模型]]"
  - "[[步骤]]"
  - "[[测试]]"
  - "[[浏览器控制]]"
  - "[[点击]]"
  - "[[生成代码]]"
  - "[[科技]]"
  - "[[编码体验]]"
  - "[[网页应用]]"
  - "[[网页抓取]]"
  - "[[自动执行]]"
  - "[[训练数据]]"
  - "[[项目]]"

链接: https://mp.weixin.qq.com/s/PqaEDWGJ_NxkR1SO4tzSug?clicktime=1752079693&enterid=1752079693&exptype=unsubscribed_card_recommend_article_u2i_mainprocess_coarse_sort_pcfeeds&ranksessionid=1752079679_2&scene=169&subscene=200
附件: https://mmbiz.qpic.cn/sz_mmbiz_jpg/h988a0nsgw7ayR61YGzn0U0FVLPMg7FYtorzISW0QicSgE3GJqEzKNBrVXZBk9RKibPKMClMyXQNUsHspQTfBcGA/0?wx_fmt=jpeg)
---
## Document Note

## Summary

当你在使用 Cursor或者Cline时，生成的代码质量可能会受到你选择的模型的限制。\x0d\x0aMCP 服务器是一种非常好的方式， 为这些模型提供额外的上下文，提升其生成代码的能力。

## Full Document
当你在使用 Cursor或者Cline时，生成的代码质量可能会受到你选择的模型的限制。

![[Attachments/07f24c1b7baf59e2223b735713cd1dcd_MD5.webp]]
MCP 服务器是一种非常好的方式， 为这些模型提供额外的上下文，提升其生成代码的能力。
![[Attachments/318a00227b1f5d5fd5b317f2d0d3471e_MD5.webp]]
以下是 MCP 服务器在 Cline中的五大主要用途，可以显著扩展 LLM 的能力：
🔍 搜索、📄 文档、🌐 浏览器控制、🗄️ 网页抓取和🤔 推理。

![[Attachments/95dd1b2217598ce75aa64d8b4e068c8a_MD5.webp]]
尽管 Claude 4、Gemini 2.5 和 GPT-4.1 等商业模型能完成其中一些任务， 使用 MCP 服务器依然是提升模型在 Cline中能力的好方法。

**以下是很多开发者常用的 MCP 服务器：**

Perplexity 用于研究，

Context 7 用于文档，

Puppeteer 用于浏览器控制，

Firecrawl 用于网页抓取，

Sequential Thinking 用于逻辑推理。

![[Attachments/750512d602bc32f8ac28881b6d0c0a30_MD5.webp]]
下面我们看看这些 MCP 服务器如何提升你的编码体验：

LLM 通常无法获取训练数据之外的信息。

Perplexity 能够实时搜索网页，获取相关数据。

当 Cline配备了 Perplexity 时，它可以将搜索结果纳入上下文中使用。

![图片](data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='1px' height='1px' viewBox='0 0 1 1' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' fill-opacity='0'%3E%3Cg transform='translate(-249.000000, -126.000000)' fill='%23FFFFFF'%3E%3Crect x='249' y='126' width='1' height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)
  

当你的项目依赖新的或经常更新的库时，Context 7 非常有用。

无需等待模型重新训练，通过 Context 7 可直接获取最新文档。

Puppeteer 可自动执行浏览器任务，如测试、导航和点击。

你甚至可以让 Cline与自己的网页应用交互。

![图片](data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='1px' height='1px' viewBox='0 0 1 1' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' fill-opacity='0'%3E%3Cg transform='translate(-249.000000, -126.000000)' fill='%23FFFFFF'%3E%3Crect x='249' y='126' width='1' height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)
Firecrawl 可抓取整个网站，并将其转换为 Markdown 格式供 Cline使用。

可用于克隆网站、批量抓取文档，或进行通用研究。

Sequential Thinking 能将复杂问题拆解为多个步骤，提升解决质量。

MCP 服务器发展迅速，能大幅提升你在 Cline中的使用体验。
