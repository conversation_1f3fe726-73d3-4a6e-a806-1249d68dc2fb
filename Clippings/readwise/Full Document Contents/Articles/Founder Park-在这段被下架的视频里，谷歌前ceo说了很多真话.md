---
人员: 
  - "[[<PERSON> Park]]"
tags:
  - articles
日期: 2024-08-15
时间: 2024-08-23 10:09:30.710563+00:00
相关:
  - "[[ai]]"
  - "[[AI]]"
  - "[[ai agent]]"
  - "[[AI Agent]]"
  - "[[anthropic]]"
  - "[[Anthropic]]"
  - "[[ascap]]"
  - "[[ASCAP]]"
  - "[[augment]]"
  - "[[Augment]]"
  - "[[bmi]]"
  - "[[BMI]]"
  - "[[chemcrow]]"
  - "[[Chemcrow]]"
  - "[[cuda]]"
  - "[[CUDA]]"
  - "[[eric schmidt]]"
  - "[[<PERSON>]]"
  - "[[gemini]]"
  - "[[<PERSON>]]"
  - "[[john d. rockefeller]]"
  - "[[<PERSON>]]"
  - "[[microsoft]]"
  - "[[Microsoft]]"
  - "[[mistral]]"
  - "[[Mistra<PERSON>]]"
  - "[[mojo]]"
  - "[[<PERSON><PERSON>]]"
  - "[[nvidia]]"
  - "[[Nvidia]]"
  - "[[openai]]"
  - "[[OpenAI]]"
  - "[[python]]"
  - "[[Python]]"
  - "[[text-to-action]]"
  - "[[Text-to-Action]]"
  - "[[tiktok]]"
  - "[[TikTok]]"
  - "[[transformer]]"
  - "[[Transformer]]"
  - "[[tsmc]]"
  - "[[TSMC]]"
  - "[[互联网]]"
  - "[[供应链]]"
  - "[[分布式计算]]"
  - "[[创业]]"
  - "[[加拿大]]"
  - "[[反垄断法]]"
  - "[[大语言模型]]"
  - "[[数字签名]]"
  - "[[数据中心]]"
  - "[[斯坦福]]"
  - "[[机器人]]"
  - "[[机器学习]]"
  - "[[版税]]"
  - "[[电力]]"
  - "[[白宫]]"
  - "[[知识]]"
  - "[[知识管理]]"
  - "[[科技]]"
  - "[[经济影响]]"
  - "[[虚假信息]]"
  - "[[计算机科学]]"
  - "[[谷歌]]"
  - "[[资本]]"
  - "[[阿拉伯]]"

链接: https://mp.weixin.qq.com/s?__biz=Mzg5NTc0MjgwMw==&mid=2247505363&idx=1&sn=8acef0d9df72eca4fc8c275ca771f739&chksm=c00933eff77ebaf9c213847e7527cd6db196ce7eff0d90cab90a9db6292f0fb59285a4eb5ad&scene=262&from=industrynews#rd
附件: https://mmbiz.qpic.cn/sz_mmbiz_jpg/qpAK9iaV2O3tliafficzspCfAhiboJtWm76KIlalTXuI94rPiaakkDrVTfHYHFGsGtuXIibFoHOaCyrrcUic7mau9GeIQ/0?wx_fmt=jpeg)
---
## Document Note

## Summary

谷歌前 CEO 施密特在斯坦福的分享中，直言当前 AI 发展存在泡沫，市场将自我调整。施密特认为，投资者热衷于 AI 项目，但难以分辨优劣，导致资金涌入。他指出，当前的前沿 AI 模型之间差距在加大，大公司需要巨额资金支持，而电力资源也将成为关键。施密特提到，谷歌在 AI 领域被 OpenAI 超越，主要是因其过于注重员工的工作与生活平衡，而初创公司更为拼搏。此外，他强调新技术（如“Text-to-Action”）的迅速发展将引发更大变革，甚至超出人们的想象。

施密特对 AI 行业的未来持乐观态度，但同时对欧洲的科技前景表示悲观，认为只有少数国家能在此领域持续领先。AI 将加剧富者愈富、穷者恒穷的现象，技术资源的获取将成为各国竞争的核心。他最后指出，AI 的发展需要组织创新，以实现真正的生产力提升。

**问题 1：**  
施密特认为当前 AI 投资涌入的原因是什么？

答案：施密特认为，投资者热衷于 AI 项目，难以分辨优劣，导致资金大量涌入，形成泡沫。

**问题 2：**  
谷歌在 AI 领域失去优势的原因是什么？

答案：谷歌过于注重员工的工作与生活平衡，而初创公司则更加拼搏，导致其在 AI 竞争中被 OpenAI 超越。

**问题 3：**  
施密特对 AI 未来的看法是什么？

答案：施密特对 AI 行业的未来持乐观态度，认为新技术的发展将引发更大变革，但同时也指出需要组织创新以实现生产力的提升。

## Full Document
![[Attachments/e51dc07c86240b386d5e6f63fe426e16_MD5.gif]]
Google 前 CEO ，「现在已经不是 Google 员工」的施密特（Eric Schmidt）前不久在斯坦福做了一次分享。

分享被拍成视频上传到斯坦福在线课 YouTube 官号，其中有 40 多分钟施密特与学生 Q&A 的环节。

因为观点太直接，说话太实在，施密特的分享上了新闻。

斯坦福官号把视频都隐藏了。

最后施密特在邮件采访中对「错误言论」表示道歉。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/qpAK9iaV2O3tliafficzspCfAhiboJtWm76K9ttWGjogdJppWo4tKHSe41XiaMEFpOpPc8clgriazIIlzKaf1kMu6Kiaw/640?wx_fmt=png&from=appmsg)
知名科技博主**阑夕**总结了施密特分享的重点内容，TLDR。文章后面也附上了施密特的全程问答。

* 现在的谷歌为什么在 AI 领域被 OpenAI 压着打？因为谷歌觉得让员工尽早回家和平衡工作比赢得竞争更加重要。如果你的员工每个星期只来公司上一天班，你怎么可能比得过 OpenAI 或是 Anthropic？
* 看看马斯克，看看台积电，这些公司之所以成功，就是因为能够卷员工，你必须要把员工逼得够紧才能获胜，台积电会让物理学博士第一年下工厂干活，你们能想象美国的博士生去流水线吗？
* 自己犯过很多错误，比如曾经觉得英伟达的 CUDA 是很蠢的编程语言，但现在 CUDA 是英伟达最牛逼的护城河，所有的大模型都要在 CUDA 上运行，而只有英伟达的 GPU 支持 CUDA，这是其他芯片撼动不了的组合。
* 还有微软跟 OpenAI 合作时自己也觉得难以置信，微软怎么能把最重要的 AI 业务外包给那种小公司啊，结果再次看走了眼，再瞧瞧苹果在 AI 上的温吞，大公司真的都官僚化了，奋斗逼都在创业。
* TikTok 给美国人上了一课，在座各位年轻人以后如果创业，能偷音乐什么的就赶紧去做——似乎是在黑 TikTok 早期纵容盗版 BGM——如果你做成了，就有钱雇佣最顶级的律师帮你擦屁股，如果你没做成，那就没人会起诉你。
* OpenAI 的星际之门在宣传时说需要 1000 亿美金，实际上可能 3000 亿都打不住，能源缺口太大了，给白宫提过建议，美国以后要么跟加拿大打好关系，水电资源丰富，劳动力便宜，而且够近，要么去和阿拉伯国家套近乎，让他们来做主权投资。
* 欧洲已经没戏了，布鲁塞尔（欧盟总部所在地）一直都在摧毁科技创新的机会，可能法国还有点希望，德国不行，其他欧洲国家就更不用提了，印度是美国盟友里最重要的摇摆州，以及美国已经失去了中国。
* 开源很好，谷歌历史上的大部分基础设施也都受益于开源，但是说实话，AI 行业的成本太高了，开源负担不起，自己投资的法国大模型 Mistral 将会转为闭源路线了，不是所有公司都愿意且有能力像 Meta 一样当冤大头。
* AI 会让富者愈富、穷人恒穷，国家也是，这是一场强国之间的游戏，没有技术资源的国家需要拿到加入强国供应链的门票，否则也将错过盛宴。
* AI 芯片属于高端制造业，产值很高，但不太可能拉动就业，你们可能没几个人去过芯片制造厂，里面全是机械化生产，不需要人，人又笨又脏，所以不要指望制造业复兴，苹果把 MacBook 的产线迁回德州不是因为德州工资低，因为根本不用再大规模雇人了。
* 历史上，电力在引入工厂之后并不比蒸汽机创造了更多的生产力，是过了大概 30 年左右，分布式电源改造了车间布局，推动组装系统的出现，再才开始了生产力的飞跃。现在的 AI 和当初的电力一样，有价值，但还需要组织创新，才能真正拿到巨大的回报，目前大家都还只是在摘取「低垂的果实」。

点击关注，发现更多 AI 创业者

#### **01**

#### **三个会改变未来的 AI 技术**

**主持人：你怎么看 AI 在短期内的发展？在你这里短期的定义应该是未来一两年，是吧？**

**Eric Schmidt**：事情发展得太快了，感觉每隔六个月，我就要重新做一次关于未来的演讲。这里有没有计算机科学专业的？有没有人能给大家解释一下，什么是百万 token 上下文窗口？

**听众**：基本的含义是，提问 prompt 可以用一百万个 token 或者一百万个词，或者其他类似的东西。

**Eric Schmidt** ：所以百万 token 意味着你可以提出一个一百万词长度的问题。

**听众**：是的，我知道这是目前 Gemini 的一个大方向。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/qpAK9iaV2O3tliafficzspCfAhiboJtWm76Kz2cXwnViccWYfKEbTLIQuicrH3BicvOricpCibsNF3yurteO4qVicBdHSR6Q/640?wx_fmt=png&from=appmsg)
Gemini 官网介绍（中文翻译为插件效果，感谢沉浸式翻译）

**Eric Schmidt：不，他们的目标是到一千万。Anthropic 已经达到了 20 万，还在继续增长。目标是一百万及以上，可以想象 OpenAI 也有类似的目标。接下来有谁能给我们一个技术定义，解释一下什么是 AI Agent 吗？**

**听众**：AI agent 就是在网上执行任务，代表你来购买东西，以及类似的各种操作。

**Eric Schmidt** ：所以 agent 就是执行某种任务的东西，另一个定义是一个具有记忆功能的大型语言模型。再问一个问题，计算机科学的同学，有人能解释一下什么是 Text-to-Action 吗？

**听众** ：就是把文字扩展到更多文本，输入文本，然后 AI 根据文本触发操作。

**Eric Schmidt**：另一个定义是把语言转换成 Python——一种我从没想到还能继续存活的编程语言。但现在 AI 的一切都是用 Python 来做的。最近有一种刚刚发布的新语言叫 Mojo，它似乎终于解决了 AI 编程的问题，不过我们还要看看在 Python 统治局势下，它能不能生存下来。

**再问一个技术问题，为什么 Nvidia 价值两万亿美元，而其他公司却陷入困顿？**

**听众** ：技术原因嘛。我认为这主要归结于代码运行的优化。目前大多数代码需要在经过优化的环境中运行，而目前只有 Nvidia 的 GPU 可以做到这一点。事实上其他公司有能力开发各种技术，可能拥有长达十年的软件开发经验，但它们没有专门针对机器学习进行优化的团队。

**Eric Schmidt**： 我喜欢把 CUDA 看作是 GPU 的 C 语言。这是我喜欢的理解方式。它在 2008 年诞生，我一直觉得它是个糟糕的语言，但它却成为了主流。现在有一整套开源库，它们都是针对 CUDA 高度优化的。构建这些技术堆栈的所有人都忽略了这一点。我们称之为 vlm 技术，加上其他类似的开源库，它们都为 CUDA 做了优化。这对竞争对手来说很难复制。

**以上这些意味着什么？**

在接下来的一年里，你会看到更大规模的上下文窗口、Agent 和 Text-to-Action 的功能。当它们被大规模应用时，影响将比我们现在看到的社交媒体带来的巨大冲击还要大，至少在我看来是这样。在上下文窗口里，你可以把它当作短期记忆来用，规模能做得这么大，这太让人震惊，技术上服务和计算是非常复杂的。

短期记忆的有趣之处在于，让它读 20 本书，把这些书的文本输入进去作为查询，让它告诉你书的内容。人类大脑会忘记中间的部分。现在有一些人在构建基本的 LLM Agent。它们的工作方式是，比如读化学类的内容，发现其中的化学原理，然后进行测试，再把结果加入到它们的理解中。这非常强大。

第三点，就是我提到的文本到动作。举个例子，政府现在正在考虑禁止 TikTok。我们不知道会不会真的发生。如果 TikTok 被禁了，我建议你们对你们的 LLM 说：复制一个 TikTok，获取所有用户，获取所有音乐，加入我的偏好，30 秒内生成并发布。如果一个小时内没火，那就换个类似的做法，这就是命令。砰砰砰，马上就成了。

你明白吗？如果你能从任意语言直接生成任意的数字指令，这基本上就是这个场景下 Python 的作用。想象一下，每个人都有一个能按你要求工作的程序员，而不再是那些为我工作，但不听话的程序员。（笑）程序员们都知道我在说什么。想象一下，一个不自大的程序员，真正按你的要求去做，还不用付那么多钱。而且这些程序员是无限供应的。而这些……

**主持人**：都会在未来一两年内实现。

**Eric Schmidt**：很快就会实现。我非常相信它们会在下一波技术浪潮中发生。

**听众：你提到扩展上下文窗口、代理和 Text-to-Action 的结合将带来难以想象的影响。首先，为什么这些结合很重要？其次，我知道你无法预知未来，但你为什么认为这会超出我们目前的想象？**

**Eric Schmidt**：我认为主要是因为扩展上下文窗口能够解决时效性的问题。当前的 AI 模型大约需要一年时间来训练，包括 6 个月准备，6 个月训练和 6 个月微调，所以它们总是有点滞后。但扩展后的上下文窗口可以让你输入最新的信息，这样的上下文功能非常强大，就像谷歌那样能够实时更新。

关于 Agents 模型，我举个例子。我建了一个基金会，资助了一个非营利组织，他们启动了一个项目，有一个叫做 Chemcrow 的工具，它是基于大语言模型的系统，用来学习化学知识。他们用这个系统生成蛋白质方面的化学假设，然后实验室会在晚上做测试，系统再继续学习。这极大加快了化学和材料科学领域的研究进展。

我认为「Text-to-Action」可以理解为大量廉价程序员带来的效果。不过我觉得我们还没有真正理解，当每个人都有一个自己的程序员的时候会发生什么，他们做的是你的专长，不是简单的开关灯那样的事。

你可以设想一个场景，比如你不喜欢 Google。就说，帮我造一个 Google 的竞争对手，搜索网页、搭建界面、加入生成式 AI，30 秒内做好，我们来看看效果。这些老牌公司，比如 Google，就很可能会受到这种攻击的威胁，我们等着看。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/qpAK9iaV2O3tliafficzspCfAhiboJtWm76KPUiaPYkKLJ1uKHUtr48nlO1vOSbJhazHx4tnJOhfOSEn3V3jg4elZew/640?wx_fmt=png&from=appmsg)
#### **02**

#### **「我已经不是 Google 员工了」**

**主持人**：你在 Google 工作了很多年，他们发明了 Transformer 架构，Peter（Peter Norvig，前 Google Research 的工程总监）是主导者之一。感谢像 Peter 和 Jeff Dean 这样的聪明人。不过现在，Google 似乎已经在主动权上失去了优势，OpenAI 已经赶上来了。我看到的最新排名中，Anthropic 的 Claude 排在了前面。我问过 Sundar（桑达尔·皮查伊），他没有给我一个明确的回答。也许你有一个更清晰或客观的解释，说说那里到底发生了什么。

**Eric Schmidt**：我已经不是 Google 的员工了。坦率地说，Google 更加注重工作与生活的平衡，早早下班和居家办公，似乎比打胜仗更重要。**初创公司的成功秘诀就在于员工拼命工作**。我很抱歉，说得这么直接，但事实就是如此。如果你们毕业后创办公司，你们不会让员工每周只来公司一天，大部分时间在家工作。如果想和其他初创公司竞争，这样做是行不通的。

**主持人**：Google 早期的情况和当时的微软很像……

**Eric Schmidt**：是的。

在我们这个行业，有一种常见的现象：**一些公司以非常创新的方式赢得市场，彻底主导了一个领域，但却无法顺利过渡到下一个阶段**。

这种情况有很多。我认为创始人很重要，这是非常重要的问题，他们掌舵公司。虽然创始人往往难以相处，对员工要求苛刻，但他们也推动了公司向前发展。

尽管我们可能不喜欢 Elon（马斯克）的一些个人行为，但看看他在工作上做了什么。我和他共进晚餐那天，他一直在来回飞行。我当时在蒙大拿，而他那天晚上十点还要飞去参加凌晨与 xAI 的会议。

我去台湾的时候，感受到不同的地方有不同的文化，我印象深刻的是，台积电（TSMC）有一个规定，新入职的物理学博士要先在工厂地下室工作。你能想象让美国的博士去做这种工作吗？几乎不可能。

工作结果是不同的。我之所以对工作的问题如此苛刻，是因为这些系统存在网络效应。时间非常关键，而在大多数行业中，时间并不那么重要，他们有足够的时间。可口可乐和百事可乐会一直存在，两者的竞争也会持续下去，像冰川一样缓慢变化。

当我与电信公司合作时，一般的电信合同需要 18 个月才能签署。我觉得没必要这么久，事情应该尽快完成。我们现在正处在增长和收益的高峰期，这时候还需要一些疯狂的想法。

比如微软决定与 OpenAI 合作时，我当时觉得那是最愚蠢的想法之一。**微软把 AI 领导权交给了 OpenAI 和 Sam 的团队，这简直不可思议。**然而今天，他们正逐步成为最有价值的公司之一，与苹果的竞争不相上下。苹果在 AI 方面没有好的解决方案，看起来微软的策略奏效了。

#### **03**

#### **模型的差距正在拉大**

**Eric Schmidt**：你刚才问，接下来会发生什么，每隔六个月，我的想法都会有所摇摆。我们现在处于一个奇偶震荡的周期波动中。就目前来看，前沿模型之间的差距——现在只有三种模型——和其他模型之间的差距似乎在拉大。六个月前，我还认为差距在缩小，所以我投了很多钱给一些小公司，不过现在我不那么确定了。

我开始和大公司谈，大公司告诉我，**他们需要 100 亿、200 亿、500 亿，甚至 1000 亿资金。**

**主持人：目标是 1000 亿，对吧？**

**Eric Schmidt**：是的，很难很难。我和 Sam Altman 是好朋友，他认为可能需要 3000 亿，甚至更多。我告诉他，我已经计算过所需的电力了。我上周五去了白宫，开诚布公告诉他，我们需要和加拿大搞好关系，因为加拿大不仅人好，还帮助发明了 AI，并且有很多水电资源。而我们国家没有足够的电力来支撑这个发展。

另一个选择是让阿拉伯国家出资。我个人很喜欢阿拉伯，也在那里呆过很长时间。但他们不会遵守我们的国家安全规则，而加拿大和美国是可以一起合作的。

**主持人**：没错。所以这些价值 1000 亿、3000 亿的数据中心，**电力会变成稀缺资源。**

**Eric Schmidt**：是的。顺着这个思路，如果 3000 亿都要投到 Nvidia 身上，你知道该买什么股票了，对吧？（笑）当然，我不是在推荐股票。

**主持人**：没错。我们将需要更多的芯片，Intel 正从美国政府获得大量资金，还有 AMD，他们都在努力建造芯片工厂。

**Eric Schmidt**：如果现场有使用 Intel 芯片的设备，请举手（听众举手）。它的垄断似乎到此为止了。

**主持人**：Intel 曾经确实是垄断者。而现在是 Nvidia 的垄断。那么，像 CUDA 这样的技术壁垒，是否有其他公司可以做？我前几天和另一位创业者聊过，他会根据能获得的资源，在 TPU 和 Nvidia 芯片之间切换使用。

**Eric Schmidt**：因为他没有其他选择。如果他有无限的资金，今天他肯定会选择 Nvidia 的 B200 架构，因为那样速度更快。我不是在暗示什么，竞争当然是好事。我和 AMD 的 Lisa Sue（苏姿丰）详细讨论过这个事情，他们开发了一个系统，可以将 CUDA 架构转换成他们自己的架构，叫做 Rocm。目前还没完全发挥作用，他们还在继续改进。

#### **04**

#### **我们会经历一场巨大的泡沫，**

#### **然后市场会自己调整**

**听众：你对 AI 的前景非常乐观。你觉得是什么推动了这种进步？是更多的资金？还是更多的数据？或者是技术上的突破？**

**Eric Schmidt**：我基本上是看哪个项目都投，因为我也说不准哪个能成。而且，现在有一大堆资金跟着我一起进来。我觉得，部分原因是早期投资已经赚到钱了，现在那些大资金的投资者，虽然他们不太懂 AI，但他们觉得每个项目都得加点 AI 元素，所以现在几乎所有的投资都变成了 AI 投资。他们分不出好坏。**我理解的 AI，是那种真正能学习的系统，我认为这才算数。**

另外，现在有些非常先进的新算法，它们已经不局限于 Transformer 架构了。我有个朋友，也是我长期的合作伙伴，他做出了一种全新的非 Transformer 架构，我在巴黎资助的一个团队也说他们有类似的创新，斯坦福这边也有不少新动向。

最后，市场上普遍相信，开发智能技术会带来巨大的回报。比如说，你给一家公司投了 500 亿美元，那你肯定希望通过智能技术赚回一大笔钱。所以我们可能会经历一个巨大的投资泡沫，然后市场会自我调整。过去一直都是这样，现在可能也不例外。

**主持人**：你之前提到，现在头部公司正在越拉越开距离。

**Eric Schmidt**：对，现在确实是这样。法国有家公司叫 Mistral，他们做得很好，我也投资了他们。他们推出了第二版模型，但第三版可能会是封闭的，因为成本太高。他们需要收入，不能再免费提供模型了。

开源和闭源之间的争论在我们行业里非常激烈。我个人的整个职业生涯都建立在人们愿意分享开源软件的基础上。我做的技术工作都是开源的，谷歌的很多核心技术也是开源的。但是现在可能**因为资本成本实在太高，软件的开发方式可能会发生根本性的变化。**

我个人觉得，软件程序员的生产力至少会翻倍。现在有三四家软件公司在努力实现这个目标，我也投了这些公司。他们的目标是提升软件程序员的效率。我最近见到的一个很有趣的公司叫 Augment。我总是想着单个程序员，但他们的目标其实是那些大型软件团队，这些团队可能有几百万行代码，但没人能搞清楚所有代码的运行细节。这个问题非常适合用 AI 来解决。他们能赚钱吗？我希望能。

**主持人**：所以，还有很多问题要讨论。

**听众：关于非 Transformer 架构，我觉得状态模型之类的架构大家讨论得不多，但现在它们又有了更多的进展，你在这个领域看到了哪些新进展？**

**Eric Schmidt**：我对数学了解不够深，这里的数学非常复杂。但基本上，它们就是用不同的方法来做梯度下降和矩阵乘法，速度更快、更好。Transformers 是一种同时进行乘法运算的系统化方式，我是这么理解的。它跟这个类似，但数学原理不同。

**听众：你是工程师出身，考虑到这些模型未来可能具备的能力，我们是否还需要花时间学编程？**

**Eric Schmidt**：这就好比你已经会说英语了，为什么还要继续学英语呢？学习总是能让人更上一层楼。你得理解这些系统的工作原理。

#### **05**

#### **分布式计算解决不了**

#### **AI 的算力问题**

**听众：两个简单的问题：一是大型语言模型的经济影响，是否比你最开始预计的市场影响更慢？二是你认为学术界应该获得 AI 补贴吗？还是应该跟大公司合作？**

**Eric Schmidt**：我一直在努力推动为大学建立数据中心。如果我是这里的计算机科学系的教授，我会非常不满意，因为我没办法和研究生们一起开发那些算法，而且还被迫跟那些大公司合作。在我看来，这些公司在这方面做得并不够。我和一些教授聊过，他们很多人都得花大量时间等 Google Cloud 的使用配额。这是一个蓬勃发展的领域，正确的做法就是把资源提供给大学，我正在努力推这件事。

至于你提到的劳动力市场的影响问题，我基本上相信，高技能型的大学教育和相关的工作应该会没问题，因为人们会和这些系统一起干活儿。我觉得这些系统和之前的技术浪潮没什么不同，那些危险的工作和不太需要人类判断的工作最终会被替代。

**听众：你有没有研究过分布式环境？我问这个是因为，搭建大型集群很困难，但 MacBook 还是很强大的。全世界有很多小型机器。你觉得像 Folding@home 的想法能用来做训练吗？**

注："Folding@home" 是一个利用全球分布式计算资源的项目，利用全球参与者的电脑闲置资源来进行蛋白质折叠的计算。

**Eric Schmidt**：分布式环境的确是个挑战。搭大型集群确实不容易，但每个 MacBook 都有自己的算力。全球有那么多小型机器，把它们联合起来的想法确实有潜力。这可以用来做训练，但还有很多技术细节需要解决。

我们深入研究过这个问题，这些算法的工作原理是这样的：你有一个非常大的矩阵，基本上就是进行乘法运算。你可以想象这个过程是反复进行的。这些系统的性能完全取决于数据从内存传输到 CPU 或 GPU 的速度。实际上，Nvidia 的下一代芯片已经把这些功能都集成到了一个芯片上，现在这些芯片已经非常大，功能都集成在了一起。而且封装过程非常精细，芯片和封装都是在无尘室里完成的。所以目前来看，超级计算机和光速传输，尤其是内存之间的互连，才是关键因素。因此，我认为在短期内实现你说的这一点的可能性不大。

**主持人：有没有可能把大语言模型拆分开来？**

**Eric Schmidt**：要这么做，你得有上百万这样的模型。而且你提问的方式会变得非常缓慢。

#### **06**

#### **未来我们可以不理解 AI，**

#### **但需要知道它们的边界**

**主持人**：我想换个话题，谈点哲学性的东西。去年你和 Henry Kissinger（基辛格）、Daniel Huttenlocher（丹尼尔·赫滕洛彻）[一起写了一篇文章](https://mp.weixin.qq.com/s?__biz=Mzg4NDQ4MTgxNg==&mid=2247488930&idx=1&sn=a5287291cd37465cd9bd8458938641cf&scene=21#wechat_redirect) ，探讨了知识的本质及其演变。我最近也跟别人聊到过这个话题，大多数历史时期，人类对宇宙的理解带有神秘色彩，直到科学革命和启蒙运动的到来。你们的文章中说，现在的模型变得越来越复杂、难以理解，以至于我们对它们的内部机制不再那么清楚。

费曼曾经说过，**「我创造不出的东西，我也无法理解。**」这句话我最近也提过，但目前看来，人们似乎在创造一些连自己都不太明白的事物。这是否意味着我们对知识的理解正在发生转变？我们是否需要开始接受这些模型的结论，即便它们无法给出清晰的解释？

**Eric Schmidt**：让我打个比方，这有点像年轻人。如果你家里有青少年，你知道他们是人，但不是总能知道他们是怎么想的。然而，我们的社会已经学会了如何适应他们的存在，并且知道他们终将成熟。所以，我们可能会有一些知识系统，**我们无法完全理解，但我们能够了解它们的边界。我们知道它们能做什么，不能做什么**。这可能已经是我们能够期待的最佳结果了。

**主持人：您觉得我们能掌握这些限制吗？**

**Eric Schmidt**：我觉得我们能搞定。我们每周讨论的小团队都觉得，我们将来可能会用上那种对抗性的 AI 技术。想象一下，将来会有公司专门做这个，你给他们钱，他们就帮你测试 AI 系统，找漏洞，就像现在的那种「红队」一样，只不过这次用的是 AI。整个行业都会搞起这种 AI 对抗 AI 的事情，特别是那些我们还搞不太懂的部分。我觉得这挺靠谱的。斯坦福那边也可以考虑一下这个方向。如果有研究生对怎么破解这些大模型感兴趣，研究它们的工作原理，这对他们来说是个不错的技能点。所以我觉得这两件事会一起进步。

**听众：刚才您提到与对抗性 AI 相关的评论，除了显而易见的提升 AI 性能模型之外，还有什么问题是我们需要解决的？为了让 AI 真正做我们想要的事，主要挑战是什么？**

**Eric Schmidt**：确实要提升更高性能的模型。你必须假设，随着技术进步，AI 的幻觉会有所减少，虽然我并不是说它会完全消失。你还得假设有方法来验证效果，所以我们需要知道结果是否达到了预期。

比如我刚提到的 TikTok 竞争者的例子。顺便说一句，我并不是建议你们非法窃取所有人的音乐。如果你是硅谷的创业者——我希望你们都会成为这样的创业者——**如果你的产品火了，那你就会请一大批律师来帮你解决问题；但如果没人用你的产品，那么就算你盗用了所有内容，也没什么关系**。但别把我这话当真啊。

硅谷会进行这些测试，并且解决这些问题。这是我们通常的处理方式。所以我相信，将来我们会看到越来越多的高性能系统，测试也会越来越精细，最终会有对抗性测试来确保 AI 在可控的范围内。在技术上，我们称之为「链式思维推理」。人们预期，未来几年内，你将能够生成 1000 步的链式推理，就像按照食谱做菜一样。你可以按照食谱一步步来，然后验证最终的结果是否正确。系统就是这么运作的。当然，除非你是在玩游戏。

#### **07**

#### **虚假信息短期看起来无解**

**听众：如何防止 AI 在公众舆论中制造虚假信息，尤其是在即将到来的选举中？从短期和长期来看，有什么解决方案吗？**

**Eric Schmidt**：在即将到来的选举中，甚至全球范围内，大多数虚假信息都会通过社交媒体传播，而且目前社交媒体公司还没有足够的力量来管理这些信息。如果你看看 TikTok，有人批评 TikTok 偏向某种虚假信息，而不是另一种。我觉得我们在这方面乱成了一团，我们需要学习怎样批判性思考。这可能是个艰巨的挑战，但仅仅是有人告诉你某件事，不意味着它就是真的。

**听众：会不会走向另一个极端？真事反而没人相信了？有人概括这种现象为「认识论危机」。**

**Eric Schmidt**：我觉得我们现在面临一个信任危机。我认为，对社会来说最大的威胁是虚假信息，因为我们在制造虚假信息这方面会越来越厉害。我管理 YouTube 的时候，遇到的最大问题是，人们会上传假视频，甚至让有人因此命都没了，我们当时还有个「无死亡政策」，听起来很震惊吧。

注：YouTube 不允许任何鼓励危险或非法活动的内容，这些活动可能导致严重的身体伤害或死亡。

想试着解决这些问题真的很痛苦，那时候还没有生成式 AI。所以说实话，我没特别好的解法。

**主持人**：技术手段不是万能的解决办法，但有一个看起来可以缓解这个问题的方法，就是公钥认证。比如说，当拜登上台演讲的时候，为什么不能像 SSL 那样给他说的话加上数字签名呢？或者名人或公众人物发言时，他们能不能有自己的公钥呢？就像我把信用卡信息给到亚马逊时，我知道收件方确实是亚马逊。

**Eric Schmidt**：这确实是一种公钥认证的方式，再加上其他验证手段，确保我们知道信息的来源。

我跟人合写过一篇论文，支持的就是你刚才论点，可惜的是，这篇论文完全没起到什么作用。所以可能系统并没有像你说的那样被组织起来解决这个问题。

总体来说，CEO 们的目标都是追求最大化收入，为了做到这一点，他们必须追求用户的最大参与度。**要最大化参与度，就意味着要激发更多的愤怒情绪。**算法会优先推送那些让人愤怒的内容，因为那样能带来更多收入。所以，整体上存在一种偏向极端内容的倾向，而且这不分阵营。这是我们的社会中必须要解决的问题。

关于 TikTok 的解决方案，我们之前私下聊过。小时候，有个叫做「平等时间规则」的规定。因为 TikTok 其实并不是社交媒体，它更像是电视，是有程序员在控制内容的。有数据显示，美国的 TikTok 用户平均每天花 90 分钟看 200 个视频，这数量相当大。政府可能不会去制定平等时间规则，但某种形式的平衡是必要的。

#### **08**

#### **大模型是少数国家才有资格参与的竞争**

**听众：就国家安全或利益来说，你认为 AI 在与中国的竞争中会发挥什么作用？**

**Eric Schmidt**：我曾担任 AI 委员会主席，这个委员会详细研究了这个问题。报告有 752 页，你可以去看看。我简单总结一下：我们现在领先，我们需要继续保持领先地位，而且需要大量资金来实现这一点。

大致情况是，如果前沿 AI 模型继续发展，少数开源模型也参与进来，那么只有少数几个国家有资格参与。那些拥有大量资金、强大教育体系，并且有取胜决心的国家。美国是其中之一，中国也是。也许还有其他国家。但可以肯定的是，在你们有生之年，美国和中国之间的知识领域的竞争将是最大的对抗。

美国政府基本上已经禁止向中国出口 Nvidia 芯片，虽然他们不允许说这个，但确实是这么做的。我们在芯片技术上大约领先中国 10 年。在光刻机技术方面，我们也领先了大约 10 年。未来我猜我们还能再领先几年。芯片法案是特朗普政府的决定，并得到了拜登政府的批准。

**主持人：您认为当前政府和国会是否听取您的建议？您认为他们会进行这么大规模的投资吗？除了芯片法案之外，是否会继续建设大型 AI 系统？**

**Eric Schmidt**：正如你所知，我领导了一个非正式的小组，这个小组不是官方性质的小组，这个小组包括了所有常见的 AI 领域的参与者。过去一年里，这些参与者提出的建议成为了拜登政府 AI 领域决策的基础，这个法案可能是历史上最长的总统指令。

注：美国拜登总统于去年 8 月 9 日发布的《关于解决美国在特定国家对某些国家安全技术和产品的投资问题的行政命令》（Executive Order on Addressing United States Investments in Certain National Security Technologies and Products in Countries of Concern）

**主持人**：你正在推进特别竞争研究项目。

**Eric Schmidt**: 这是行政办公室的实际执行法案。他们正在忙于落实细节，到目前为止做得不错。举个例子，去年我们讨论过一个问题：如何检测系统中的潜在危险。这种系统可能已经学到了一些危险的东西，但你却不知道该问什么。换句话说，这是个核心问题。系统学到了一些不好的东西，但它不会告诉你学到了什么，而你也不知道该怎么提问。这里面有很多威胁，比如它可能学会了你不了解的化学混合方式。所以现在很多人都在努力解决这个问题。

最终，我们在备忘录中设定了一个阈值，叫做 10^26 次方浮点运算，它是一种计算能力的衡量标准。超过这个阈值时，你必须向政府报告你的行为。这是规则的一部分，欧盟设定的阈值是 10 的 25 次方，但差别不大。我认为这些技术区别最终都会消失，现在的技术可以进行「联邦训练」，也就是可以将不同部分组合起来进行训练。所以我们可能无法完全避免这些新技术带来的威胁。

**主持人** ：听说 OpenAI 已经不得不这么做，部分原因是因为电力消耗太大，没有一个地方能单独承担所有的计算量。

#### **09**

#### **AI 是有钱人的游戏，**

#### **富者愈富**

**听众：《纽约时报》起诉 OpenAI 用他们的作品训练模型。您认为这对数据使用意味着什么？**

**Eric Schmidt**：我在音乐版权方面有很多经验。在 60 年代，有一系列诉讼，最终达成了一个协议，就是每次你的歌曲被播放的时候，无论听众是否知道你是谁，你都会得到一定的版税，这笔钱会被存入你的银行账户。我猜未来的情况也会类似，会有很多诉讼，最终达成某种协议，规定使用这些作品时必须支付一定比例的收入。你可以看看 ASCAP（美国作曲家、作家和发行商协会）和 BMI（Broadcast Music, Inc.，一家美国表演权组织）的例子，虽然看起来有点过时，但我认为最终的情况会是这样。

**听众：看起来有几家公司在主导且会继续 AI 领域，这些公司似乎正是所有反垄断法关注的对象。你怎么看这两个趋势？你觉得监管机构会拆分这些公司吗？这会对行业产生什么影响？**

**Eric Schmidt**：在我的职业生涯中，我曾经推动过拆分微软，但它并没有被拆分。我也努力让谷歌不被拆分，结果它也没被拆分。所以在我看来，只要这些公司避免成为像 John D. Rockefeller（标准石油公司创始人）那样的垄断巨头，趋势就不是拆分。这就是反垄断法的由来。

我不认为政府会采取行动。你看到这些大公司主导市场的原因是，只有它们有资金建造这些数据中心。所以我的朋友 Reed Hastings（Netflix 联创兼 CEO）和 Elon Musk 都在这样做。

所以富者愈富，穷者只能尽力而为。这是事实，这是富国的游戏，需要巨额资本、大量技术人才和强有力的政府支持。还有许多其他国家有各种问题，他们没有这些资源，所以他们必须与其他国家合作。

**听众：你花了很多时间帮年轻人创造财富，对这件事很有热情。对在座的同学们职业生涯的这个阶段以及未来，有什么建议吗？**

**Eric Schmidt**：我对你们快速展示新想法的能力印象深刻。在我参与的一个黑客松中，获胜团队的任务是让无人机在两座塔之间飞行。他们在一个虚拟无人机空间里完成了这个任务，让无人机理解了「在…之间」的意思，用 Python 写了代码，在模拟器中成功让无人机穿过了塔楼。如果是专业程序员来做这件事，可能需要一两周的时间。

我要说的是，**快速制作原型的能力确实非常重要**。作为企业家，问题之一就是一切都发生得非常快。现在，如果你不能在一天内利用各种工具做出原型，你就得好好想想了，因为你的竞争对手能完成。

所以我的建议是，当你开始考虑创业，写一份商业计划是好的，你应该让电脑帮你写商业计划，用这些工具快速将你的想法转化为原型是非常重要的。因为可以肯定的是，在另一家公司、另一所大学或者你从未去过的地方，有人也在做同样的事。

[![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/qpAK9iaV2O3sAVsSPfCN9UX44XiaoicbUJIqSBuicUxZh7Gk9P7e64GsY96M2ibY3fSgldrt7WbtLdkzZHLh4Npeu3A/640?wx_fmt=png&from=appmsg)](http://mp.weixin.qq.com/s?__biz=Mzg5NTc0MjgwMw==&mid=2247498686&idx=1&sn=73438da0762cdc2b2f77097a24804dfb&chksm=c0091d82f77e9494acd591196c48d2c197348d95d19b8590e1c43dc26e7bb10bc367506b6e93&scene=21#wechat_redirect)

**更多阅读**

[外网玩疯了，NSFW、毫无底线，FLUX 成为图片生成新王者](http://mp.weixin.qq.com/s?__biz=Mzg5NTc0MjgwMw==&mid=2247505344&idx=1&sn=32b37dcb942660708db2ad8179560f9f&chksm=c00933fcf77ebaeadf5f486ef9fac4562fde33587363545ef89336c0c29af818a7b2a17d58e1&scene=21#wechat_redirect)  

[资源多到什么程度，才能创业追 OpenAI？](http://mp.weixin.qq.com/s?__biz=Mzg5NTc0MjgwMw==&mid=2247505271&idx=1&sn=1b1a9c96fb0fe2e7fe159e033837d2b7&chksm=c009334bf77eba5d3628df6f05d7b6adcb57a6ffb6ff0b591c1a29ed826e71a15b29e0e7255c&scene=21#wechat_redirect)  

[2000万用户，Gamma创始人：PPT是痛点，但产品好才能解决痛点](http://mp.weixin.qq.com/s?__biz=Mzg5NTc0MjgwMw==&mid=2247505082&idx=1&sn=9bb5a417eb7cef8012d46241d2fe3558&chksm=c0093286f77ebb90100a0ca17ba71ba3423e398cfce77217de7179b74b5b92577ee437cb777f&scene=21#wechat_redirect)  

[张鹏对话夏勇峰：使用时长超过 5 小时的 AI 硬件，才能留在牌桌上](http://mp.weixin.qq.com/s?__biz=Mzg5NTc0MjgwMw==&mid=2247505008&idx=1&sn=05f0ba2dc24bec34c8acdc44b75a370b&chksm=c009324cf77ebb5a6530dec4852ac3d5d7d70f831d3b9ea631fdd8863cb5a14eaa18ab799526&scene=21#wechat_redirect)

转载原创文章请添加微信：founderparker
