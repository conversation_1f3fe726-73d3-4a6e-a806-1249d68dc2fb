---
人员: 
  - "[[吕立青_JimmyLv]]"
tags:
  - articles
日期: 2023-07-10
时间: 2024-08-09 12:26:56.814163+00:00
链接: https://sspai.com/post/80910
附件: https://cdn.sspai.com/sspai/assets/img/favicon/icon.ico)
---
## Document Note

## Summary

展信佳，曾经有无数新的机会摆在我面前，我没有珍惜，等我失去的时候，我才后悔莫及，人世间最痛苦的事莫过于此。如果上天能够再给我一次机会，我会对那个笔记工具说三个字：我选你。如果非要为这个选择作出一个解释 ...

## Full Document
展信佳，

> 曾经有无数新的笔记工具摆在我面前，我没有珍惜，等我失去的时候，我才后悔莫及，人世间最痛苦的事莫过于此。如果上天能够再给我一次机会，我会对那个笔记工具说三个字：我选你。如果非要为这个选择作出一个解释，我希望是……这份信。
> 
> 

![[Attachments/3f9c4a44d7d207ef0cc0c3950ae65ab7_MD5.gif]]#### **📝 笔记工具市场：Roam Research 的模仿者**

在工具层面，同类型对比时，唯一的对手只有 Logseq，同为 Clojure & ClojureScript 开发，文学编程的默契“同源”。而且 Logseq 是开源的，融资额 400w 美元，现金充裕，生态很好，之前的 Roam Research 大V Ramses Oudt 被雇佣专门做 Marketing，最新官网的描述也很棒：[Logseq: A privacy-first, open-source knowledge base.](https://logseq.com/)

Obsidian，作为最先的模仿者，但是选择了 Markdown 文件作为载体，所以受限很多，跟 Logseq 相比而言，在数据格式上并不占优。Logseq 和 Obsidian 都主打 local-frist，私有化数据，就像 Obsidian 的口号：[A second brain, for you, forever.](https://obsidian.md/)

但是对于普通用户来说，并不关心背后的 Markdown 或者是 Datalog 数据库，所以依然需要同步，自选方案当然有，比如 iCloud，但是 Apple 确实拉胯，导致基于文件系统的同步体验着实很差， 并且经常可能出错。

#### **💰 商业化变现：Obsidian Publish 和 Logseq Pro**

于是乎，Logseq 和 Obsidian 这对难兄难弟都选择推出了自己的 Pro 服务，即自带云同步。Obsidian 更甚，基于 Markdown 的好处在于面向文档，于是 [Obsidian Publish](https://obsidian.md/publish) 孕育而生，作为内容的托管服务，取代博客，且模仿了 [Andy's Working Notes](https://notes.andymatuschak.org/) 样式，名为「数字花园」。只不过，同样价格很贵。

Logseq 本身是开源的，且天然支持 Git，从而程序员群体比较偏爱它。同时，爱折腾的程序员们，自然会更容易找到公开发布的方案，也更愿意为它开发插件。需要注意的是，Obsidian 本身并不开源，但它的插件生态和开发者文档做得很好。于是，诸多插件如雨后春笋般涌现，产品体验也随之走向了崩坏，原因也很简单：插件不考虑宿主，更不考虑用户。

#### **💻 本地化 vs. 云端化：新兴的云笔记工具们**

说完了本地化的两款代表笔记工具之后，基于云的 Roam Research 模仿者，值得一提的就只有两款：RemNote 和 Tana。RemNote 依托于靠谱的科学研究，较为重视 **Rem**ember Note 的相关产品设计，比如 Flashcard 等机制，对学生来说价值更高。

当然，继[双链](https://roamresearch.com/#/app/JingLv/page/BWVvutQ31)的概念大热之后，市场上既有的类似产品也随之跟上，最典型的比如 Workflowy 是我看好的一名选手。多年的功能开发之后，却保持了极简，着实不易。双向链接的引用，取名为 [mirrors](https://workflowy.com/feature/mirrors/) 也比较贴切，跟 Notion 的 [synced block](https://www.notion.so/blog/designing-synced-blocks) 有异曲同工之妙，但也仅限于此了。

而 Tana 则是新的搅局者，第二大脑的作者 Tiago Forte 称之为 "the new Roam"，而社区更倾向于将其描述为 Notion + Roam Research 的孩子。Tana 的早期起势非常高调，社区大 V 们纷纷高潮，奔走相告，再加上邀请制一码难求，期待值非常高，堪比 Arc 浏览器。

#### **🤖 AI 的崛起：ChatGPT 和 New Bing 带来的挑战与机遇**

但是，就如 ChatGPT 所引发的一波高潮之后，微软祭出 New Bing 大杀器，让直接集成 ChatGPT 的 Edge 浏览器大杀四方。Arc 浏览器在功能层面上的创新显得微不足道，AI 加持的新生产力工具，才是未来的革命性创新。

![[Attachments/20265a8ebbf7a79a3f9e3a6787980d28_MD5.jpg]]New Bing 弥补了 GPT-3 缺失实时网络搜索结果的缺憾，聚合总结的效果更加震撼。并且，Edge 浏览器中的 New Bing 能够直接与网页的内容直接进行交互，让 ChatGPT 根据用户实时浏览的内容进行智能问答。众所众知，浏览器作为互联网的入口，所浏览的内容不局限于网页，同样可以作用于 PDF、图片等其他内容，因此想象空间更为巨大。

由此，微软所打出的 New Bing 搜索引擎 + New Edge 浏览器，让这两个领域都占据先手优势的谷歌猝不及防，已有的 Google 搜索和 Google Chrome，不得不开始跟上脚步和踩准节拍。好玩的是，谷歌联合创始人拉里·佩奇（Larry Page）和谢尔盖·布林（Sergey Brin）都被紧急召回，更频繁地参与到公司业务当中，布林甚至亲自下场为谷歌聊天机器人Bard写代码。然而被给予厚望的先发产品 Bard，还是在发布会演示时就立马栽了跟头。

#### **💡 思维链路：Roam Research 的独特价值与使用感受**

现在，让我回过头重新选择工具的话，我依然对 Roam Research 寄予厚望。并不是因为我曾经一次性充值了 5 年 believer 的所谓信仰，也不是因为我已经退出微信群运营的 RoamCN 社区，曾经的 #roamcult 社区狂热早已退散。

但如同我的实践感受一样，有一种莫名的集体潜意识让我意识到，社区也在慢慢发现 Roam Research 确实做得更好。并且，使用的人也在慢慢回流，不光是因为他们在 Logseq 或 Obsidian 等其他地方碰了壁，也是因为对笔记本身这件事情的理解。

我确信，如果自己不曾坚持使用 Roam Research，我也不会发现在深层级的功能设计和操作细节之下，隐藏的所谓“思维链路”是多么重要，更无法悟得卢曼与卡片盒“沟通”的真谛。才明白，原来 Roam Research 一直在这里，扮演着最好的“沟通对象”角色，哪怕已经有了 ChatGPT 这样更加智能的“提问对象”。

#### **🤖 ChatGPT vs. 写作：为什么选择卡片写作而非直接问 AI？**

相比之下，AI 看似智能的直给，恰恰剥夺了思考的乐趣。直给答案，代表着丝滑，同样也意味着没有摩擦。但由于缺少了必要难度，大脑无法加强存储难度，反而培养了一种遇事不决，先问 AI 的惰性，因为提取难度更高，大脑总是会选择那条最小阻力之路。

「不写，就无法思考。」代表着就是通过写作的方式来刻意制造摩擦，游戏化就是主动克服本来不必要的麻烦，但是麻烦也不能太大，总不能一来就去打终极 BOSS。于是，笔记要选择从写卡片开始，这是给自我反馈的最佳方式，也是滑板鞋在地板上摩擦的乐趣所在，时尚！

> 我的滑板鞋时尚时尚最时尚
> 
> 回家的路上我情不自禁
> 
> 摩擦 摩擦
> 
> 在这光滑的地上摩擦
> 
> 

#### **🎨 从双向链接到视觉化白板：笔记工具们的新探索**

除了笔记要卡片化这一共识以外，大家对双向链接本身的迷思陷入最深，自动化双链后，用户会不自觉地吐槽知识图谱未能成型，笔记厂商们则假惺惺地对图谱做做优化。但除了更难看之外，也开始做起了视觉化白板，便是摸到了新的方向，也将其纳为了商业化变现的 Pro 功能，既引得了用户们的欢呼，更赢得了投资者的期待。

对于视觉化白板，开头提到的那对难兄难弟 Logseq 和 Obsidian 分别称之为 Whiteboard 和 Canvas，简称 WC。我始终不确定白板的价值，只能将其拆分阶段为“小白板”和“大白板”，前者用于灵感阶段，后者用于梳理知识结构？可能吧，只是不同阶段理应使用不同的工具。

如果真的 All in 了视觉化白板，那肯定不得不提 Heptabase，内测一年多了，功能迭代飞速，每周发版从未间断。但不知为何，为了减少摩擦的白板功能，始终摩擦力巨大让人用不起来，这也变成了共识。是不是白板本身就错了呢？

#### **📈 视觉化白板：框架与灵活度如何取舍的平衡之道**

关键点在于，“大白板”和“小白板”之间，存在一个概念叫做“框架白板”，白板的灵活度本身就是汝之蜜糖，彼之砒霜。框架对应思维模型，思维导图如果算一种的话，那么它也算是一种自带框架的“白板”。

所以，在框架和灵活度之间取得平衡的，当属最老牌的第二大脑软件 TheBrain，本身就具备了父子兄三种层级的自由链接，还兼顾了大纲和思维导图的模式。更值得一提的是，使用 TB14 版本的用户，依然念念不忘 TB8 版本的扩展视图，可以称之为弥补白板灵活度的天花板，只要梳理过白板上节点关系时，尝试过拖拽节点，就会意识到它的价值所在。

TheBrain 的大纲视图，区别于 Roam Research 的大纲有一个很重要的点，它的连线，本质上是可活动的。连线可变更，所以灵活性高于子弹节点本身的移动。因此，TheBrain 更适合作为知识结构的持续迭代，在在不断梳理的过程中，断开链接，清理那些大脑自以为是的跳跃式联想。

而另一方面，学海无涯生有涯，我只能关注我所能关注的，TheBrain 在帮助梳理个人目标领域（Areas）的同时，不断让自己意识到能力的范围。因为，搭建结构的同时也是在塑造知识的边界，你需要积累更有限，但是更有用的知识卡片。

#### **🔔 后续（可能的🙈）拖更计划：**

![[Attachments/d7e137c208a1146e6b6d119618c919cc_MD5.png]]P.S. 这篇文章是使用 Roam Research 写的。

P.S.S. 这篇文章是由一个真实的人写的，来自真实大脑所激发的想法和创造力。我希望你使用 AI 自动总结这篇文章时，不会过度[“模糊化”](https://www.thepaper.cn/newsDetail_forward_21877769)。
