---
人员: 
  - "[[歸藏(guizang.ai)]]"
tags:
  - tweets
日期: 2025-07-02
时间: None
相关:
  - "[[bob]]"
  - "[[mcp]]"
  - "[[ppt]]"
  - "[[lora]]"
  - "[[word]]"
  - "[[cursor]]"
  - "[[slidev]]"
  - "[[水印]]"
  - "[[滤镜]]"
  - "[[科技]]"
  - "[[终端]]"
  - "[[node.js]]"
  - "[[todo.md]]"
  - "[[youtube]]"
  - "[[markdown]]"
  - "[[obsidian]]"
  - "[[命令行]]"
  - "[[多模态]]"
  - "[[提示词]]"
  - "[[gemini cli]]"
  - "[[任务列表]]"
  - "[[任务管理]]"
  - "[[会议纪要]]"
  - "[[关键决策]]"
  - "[[内容创作]]"
  - "[[反向链接]]"
  - "[[图像模型]]"
  - "[[工作模式]]"
  - "[[录音转写]]"
  - "[[批量修改]]"
  - "[[文件整理]]"
  - "[[知识图谱]]"
  - "[[系统设置]]"
  - "[[翻译软件]]"
  - "[[视频下载]]"
  - "[[视频转gif]]"
  - "[[谷歌搜索]]"
  - "[[文件夹路径]]"
  - "[[文档格式转换]]"

链接: https://twitter.com/op7418/status/1940342354155880938/?rw_tt_thread=True
附件: https://pbs.twimg.com/profile_images/1636981205504786434/xDl77JIw.jpg)
---
## Document Note

## Summary

本文介绍了 Gemini CLI 的强大功能及其使用方法。Gemini CLI 是一个基于命令行的多模态智能工具，能帮助用户批量处理本地文件和系统设置，无需编程基础。用户只需两步即可启动 Gemini CLI，利用其内置工具实现文档查找、编辑，Obsidian 笔记库分析，图片重命名和标注，系统工作模式一键切换等操作。它还能生成丰富美观的 PPT，剪辑视频，添加水印，批量下载 YouTube 视频，进行图片处理和不同文档格式互转。Gemini CLI 通过提示词输入实现操作，简单易用，适合普通用户提升工作效率。安装过程只需安装 Node.js 并运行一条命令，登录谷歌账号即可使用。针对命令行界面英文难题，推荐使用 Bob 翻译软件辅助理解。高级用法包括视频转 gif、文档转 PPT 等，极大便利内容创作与管理。文章详细展示了多个实用案例，强调了 Gemini CLI 在知识管理和多媒体处理方面的优势。

**问题 1：**  
Gemini CLI 对普通用户来说，为什么使用门槛低？  

答案：  
Gemini CLI 操作主要通过提示词输入完成，无需编程知识，安装简单，且支持多种本地文件批量处理，用户只需在终端输入命令即可使用，降低了技术门槛。  

**问题 2：**  
Gemini CLI 如何帮助管理 Obsidian 笔记库？  

答案：  
它能自动检索相关文档，生成带反向链接的索引文档，构建知识网络，方便用户关联、回顾和管理笔记，提升知识图谱的实用性。  

**问题 3：**  
Gemini CLI 在多媒体处理方面有哪些功能？  

答案：  
支持视频剪辑（加水印、转 gif、加音乐）、图片批量改名、加滤镜、多图拼合、批量下载网络视频及封面，满足内容创作和编辑需求。

## Full Document
这周藏师傅呕心沥血的大活来了！

Gemini CLI 不写代码帮普通人提效的的一万种用法！

- 如何低门槛两步用上 Gemini CLI  
- 批量修改系统设置  
- 编辑查找文档，生成 PPT  
- 剪辑视频、修改图片、下载视频  
-不同文档格式的互转 等等

下面是详细的教程和案例🧵

![](https://pbs.twimg.com/media/Gu17FBXWEAAajyQ.jpg)

---

等不及施工的可以先收藏

或者直接看这里：[mp.weixin.qq.com/s/Frdf\_Gh3Xhvv…](https://mp.weixin.qq.com/s/Frdf_Gh3Xhvvmw2g-zOolA)

---

来看一下我会教你用 Gemini CLI 实现哪些能力：

如何低门槛两步用上 Gemini CLI  
查找和批量编辑本地文档  
分析你的 Obsidian 笔记库，将相关的笔记链接起来  
分析本地图片内容批量修改文件名，给图片打标  
批量修改系统设置，创建你的工作模式一键开关  
为本地文档生成效果丰富美观的 PPT  
帮你剪辑和处理本地视频，加水印、转 gif、加音乐。。。  
帮你快速批量下载 youtube 等网站视频和封面  
帮你处理图片，加滤镜、加水印、改大小、多张拼合。。。  
帮你实现不同文档格式的互转，Markdown 转 Word。。。

---

简单解释一下这类产品和 Cursor 之类的区别。

首先他们是没有界面的，所有的操作都是在终端以命令行的方式展示。

然后就是也是 Agents 可以自动执行任务处理本地文件，同时内置了非常多的工具，比如谷歌搜索、阅读文件、查找文件、搜索文字、写入文件、保存记忆等，你输入 /tools 然后回车就可以让他列出目前支持的工具。

![](https://pbs.twimg.com/media/Gu17fhCXcAABmQc.jpg)

---

我不会编程是不能不能用

很多朋友说命令行是不是很复杂啊，我不会编程是不是会很难用。

其实并没有，如果你的网络环境正常，能够正常登录 Gemini CLI 的话，跟使用 Cursor 没有本质区别。

因为核心交互的时候还是主要为提示词输入框，命令行又不用你写，Gemini 写就行。

![](https://pbs.twimg.com/media/Gu17rE4X0AEB8cH.jpg)

---

如何使用 Gemini CLI

首先要做的第一步就是进入到我们的启动台，搜索终端两个字，搜到之后打开。

这时候你就看到一个空白界面里面写了些你看不懂的字，不要担心。

这里我建议我们想好要进行的任务之后，新建一个文件夹把需要的任务素材扔进去，然后按住 option 按键鼠标右键选择“将 XXXX 文件夹拷贝为路径名称”，这时候你就快速获得了这个文件夹的路径。

然后我们回到我们的终端窗口，输入 cd + 空格 + 你刚才复制的路径，接下来你终端的所有操作都只会影响这个文件夹的内容，不用担心把电脑搞坏。

![](https://pbs.twimg.com/media/Gu17_NoWsAAj2JZ.jpg)![](https://pbs.twimg.com/media/Gu18AdfWEAA2vuP.jpg)![](https://pbs.twimg.com/media/Gu18B9qW4AAA2Q0.png)![](https://pbs.twimg.com/media/Gu18D5CXkAAkqaf.jpg)

---

到这一步我们终于开始安装 Gemini CLI 了，非常简单

先去安装 Node.js

然后你只需要输入下面的内容然后回车就行。

npx <https://t.co/Fv7ddaLF2i>

安装成功你就会看到这个界面，应该会先让你选择命令行的颜色主题，然后让你选择登录方式。

这里需要注意：终端的操作大部分时间需要用上下左右方向键来操作选项，选中之后按回车确认。

你只需要选择一个自己喜欢的主题之后，选择正常的谷歌账号登录，在拉起网页登录后关掉就行。

我这个这里已经登录了，所以没有这些选项，然后你就能看到提示词输入框了。

![](https://pbs.twimg.com/media/Gu18i1MWIAACB6d.jpg)

---

恭喜你到这里，你已经完成了 Gemini 的安装。

由于用的 NPX 的安装方式，所以你以后每次关掉终端重新使用 Gemini CLI 的时候都需要输入开始的那个命令，不过不用登录了，直接就能用。

最后由于命令行本身都是英文的，可能很多人会望而却步，这个时候你可以装个 Bob 这个翻译软件，支持划词翻译，看不懂的选项直接选中划词翻译就行。

![](https://pbs.twimg.com/media/Gu19CBoWoAA5fmp.jpg)

---

来点基础用法

由于 Gemini 可以看到你的文件并且操作，而且它还有生成能力，本身模型还是多模态的，所以即使只用本身的工具也可以有很多用法。

---

查找和生成本地文档

首先是 Gemini CLI 本身支持谷歌搜索，你可以让他搜索指定内容给你写成文档，也可以对你本身的文档进行编辑。

当然搜索工具经常会限额，这个有点恶心，比如让他搜索歸藏的信息并且整理一个介绍文档。

![](https://pbs.twimg.com/media/Gu19Wn-XgAAA5ZO.jpg)

---

你也可以让他分析你保存在本地的文章之后进行改写，生成新的文章。

比如我这里就让他把 Karpathy 的软件 3.0 文章改写成适合发布的博客文章，同时生成对应的推特发布版本，也可以对于会议总结之类的文档进行分析和处理。

提示词：

读取我刚才录音转写的会议纪要 meeting\_notes.txt，总结出关键决策点，并识别出分配给我的所有待办事项，将它们以任务列表的形式添加到我的 todo .md 文件中。

根据Andrej Karpathy 软件 3.0 分享的文章，将其改写成一篇约 800字的博客文章，风格要轻松有趣。然后，为这篇文章生成 3 个适合在 Twitter 上发布的推文版本，并附上 标签

![](https://pbs.twimg.com/media/Gu19kKXXcAADguj.jpg)

---

找到你的 Obsidian 文件夹打开之后启动 Gemini CLI，然后让 Gemini CLI 查找相关的内容。

让他检索我所有的剪藏文件，找到 MCP 相关的文章，然后给我生成一个带反向链接的《MCP 剪藏内容索引》文档

每个无序列表都有文件标题以及文章的总结，最后还有链接可以直达那个文章。

![](https://pbs.twimg.com/media/Gu1-H3yXYAADdQ4.jpg)

---

Obsidian 的一个知识图谱的功能，它可以把所有有反向链接的相关文档都链接起来，形成你自己的网状笔记网络，方便你学习和回顾。

反向链接需要自己手动加，现在有了 Gemini CLI 问题解决了，可以让他帮你给你文件夹中的相关文档加反向链接。

Your browser does not support the video tag.

---

图片分析和处理

由于本身 Gemini CLI 是多模态的的，所以你的图片也可以让他帮忙处理。

比如我打开了一个全是图片的文件夹，里面的图片名字乱七八糟的，这时候就可以让他分析图片内容之后根据图片内容给图片重新命名。

![](https://pbs.twimg.com/media/Gu1-sgrW0AAxREJ.jpg)

---

我们都知道在训练图像模型或者 Lora 的时候需要对图像进行标注，大部分训练工具都是把标注放在一个跟图片命名一样的文本文件里，现在我们就可以让 Gemini CLI 来做这件事了。

可以看到他执行的非常完美，以往这些你还得找对应的工具，而且不好自定义要求，现在提示词就行。

![](https://pbs.twimg.com/media/Gu1-zQHW4AArY9U.jpg)

---

修改系统设置整理文件

Gemini CLI 除了可以读取文件和修改文件外也是可以控制系统设置的。

比如我们就可以写好自己日常对于软件和系统设置在不同工作时间的喜好，需要的时候一键完成所有操作的更改。  
这里我就让他给我关掉浏览器，然后打开 Obsidian，降低系统音量，直接进入工作模式。

Your browser does not support the video tag.

---

我们肯定也有很多时候桌面或者文件没有整理乱七八糟。

这个时候就可以让 Gemini CLI 新建文件夹进行分类和整理。

但是这里得注意，不要让他整理过大的过于重要的文件夹，不然误删了就痛苦了。

这里我就让他把刚才的图像和标注文件新建了两个文件夹分别整理了。

![](https://pbs.twimg.com/media/Gu1_P8OWwAA_T16.jpg)

---

Gemini CLI 的高级使用方法

上面都是些基本用法，你最近可能也看到了一些。

但是我发现结合一些本地软件，Gemini CLI 能实现对各种文件更加高级的处理，比如视频转 gif、youtube 视频下载、加水印、文档格式转换等。

这些就非常牛皮了，而且我们日常内容创作大部分都非常需要。

---

为本地文档生成PPT

前面我们有了文档了，但是很多时候演示的时候总不能真给人看 Markdown 文档吧，能不能生成 PPT 呢？

可以的，朋友，必须可以，比如我这里就把前面我那个 MCP 索引文档的内容直接转换为 PPT 了。

这个依赖一个叫 Slidev 的项目，它可以用类似 Markdown 文档的格式将内容变成带有丰富样式的 PPT。

你不需要知道这个项目的细节，直接用我下面的提示词生成文件之后，复制文件到这个页面（<https://t.co/zk0DYyMf1u）预览就行。>

Your browser does not support the video tag.
