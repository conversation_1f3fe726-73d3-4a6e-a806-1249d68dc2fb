---
人员: 
  - "[[AI Will]]"
tags:
  - tweets
日期: 2025-06-22
时间: None
相关:
  - "[[llm]]"
  - "[[决策]]"
  - "[[开源]]"
  - "[[微调]]"
  - "[[游戏]]"
  - "[[点云]]"
  - "[[环境]]"
  - "[[科技]]"
  - "[[网格]]"
  - "[[训练]]"
  - "[[部署]]"
  - "[[音频]]"
  - "[[dall·e]]"
  - "[[3d图形]]"
  - "[[3d数据]]"
  - "[[ai课程]]"
  - "[[langchain]]"
  - "[[npc行为]]"
  - "[[初学者]]"
  - "[[智能体]]"
  - "[[ai构建者]]"
  - "[[huggingface]]"
  - "[[transformers]]"
  - "[[克隆代码]]"
  - "[[噪声生成]]"
  - "[[图像分割]]"
  - "[[图像分类]]"
  - "[[声音转化]]"
  - "[[实践学习]]"
  - "[[扩散模型]]"
  - "[[机器学习]]"
  - "[[环境学习]]"
  - "[[目标检测]]"
  - "[[笔记本库]]"
  - "[[语音合成]]"
  - "[[语音识别]]"
  - "[[音乐标签]]"
  - "[[音频课程]]"
  - "[[3d机器学习]]"
  - "[[开源ai食谱]]"
  - "[[游戏中的ai]]"
  - "[[计算机视觉]]"
  - "[[stable diffusion]]"
  - "[[噪声生成图像]]"
  - "[[多步推理工具]]"
  - "[[大型语言模型]]"
  - "[[深度强化学习]]"
  - "[[程序化内容生成]]"
  - "[[huggingface transformers]]"
  - "[[游戏中的机器学习]]"

链接: https://twitter.com/FinanceYF5/status/1936604491123671047/?rw_tt_thread=True
附件: https://pbs.twimg.com/profile_images/1896818103016935424/ucw3T2uW.jpg)
---
## Document Note

## Summary

本文介绍了 HuggingFace 最新发布的 9 门免费的 AI 课程，内容涵盖大型语言模型（LLM）、智能体、深度强化学习、计算机视觉、音频处理、游戏中的机器学习、3D 机器学习、扩散模型等多个热门领域。这些课程均适合初学者，且全部开源，方便学习者免费获取资源。具体课程包括：

1. LLM 课程：学习如何使用 HuggingFace Transformers 训练、微调和部署大型语言模型。
2. 智能体课程：使用 LangChain 和 HuggingFace 构建多步推理的 AI 智能体。
3. 深度强化学习课程：训练智能体通过环境学习和决策。
4. 计算机视觉课程：涵盖目标检测、图像分割和分类。
5. 音频课程：将声音转化为信号，应用于语音识别、音乐标签和语音合成。
6. 游戏中的机器学习课程：介绍 AI 在游戏 NPC 行为和程序化内容生成中的应用。
7. 3D 机器学习课程：处理点云和网格等 3D 数据。
8. 扩散模型课程：学习从噪声生成图像的技术，与 DALL·E 和 Stable Diffusion 类似。
9. 开源 AI 食谱：提供现实项目的代码笔记本，方便实践和快速构建。

这些课程资源丰富，适合想系统学习 AI 各领域技术的人士，且均可通过 HuggingFace 官网免费访问。

  
**问题 1：**  
HuggingFace 的 LLM 课程主要教授哪些内容？  

答案：  
该课程主要教授如何使用 HuggingFace Transformers 来训练、微调和部署大型语言模型（LLM），帮助学习者快速掌握 LLM 的核心技术。  

**问题 2：**  
智能体课程使用了哪些工具？其教学目标是什么？  

答案：  
智能体课程使用 LangChain 和 HuggingFace，旨在教会学习者构建具备多步推理能力的 AI 智能体。  

**问题 3：**  
扩散模型课程的核心技术是什么？它与哪些知名应用相关？  

答案：  
扩散模型课程教授从噪声中生成图像的技术，这与 DALL·E 和 Stable Diffusion 等知名图像生成应用密切相关。

## Full Document
🚨 最新消息：HuggingFace 发布了 9 门免费的 AI 课程。

LLM、智能体、计算机视觉、扩散模型，甚至游戏中的 AI。

全部适合初学者，且全部是开源的。

开始免费学习 👇

![[Attachments/4e9c855fc20c6c2acf25db1e0ee0c222_MD5.jpg]]

---

1. LLM 课程

想快速掌握大型语言模型吗？

本课程将引导你通过 HuggingFace Transformers 进行 LLM 的训练、微调和部署。

[huggingface.co/learn/llm-cour…](https://huggingface.co/learn/llm-course/chapter1/1)

![[Attachments/a892370a2089b12a0e2bf0b1546fcdfa_MD5.jpg]]

---

2. 智能体课程

现在每个人都在构建 AI 智能体。

本课程教你使用 LangChain 和 HuggingFace 创建多步推理工具。

[huggingface.co/learn/agents-c…](https://huggingface.co/learn/agents-course/unit0/introduction)

![[Attachments/bca24274960f747010d9a06275906fb7_MD5.jpg]]

---

3. 深度强化学习课程

深度强化学习是 AI 开始变得“有生命”的地方。

训练智能体做出决策并从环境中学习。

[huggingface.co/learn/deep-rl-…](https://huggingface.co/learn/deep-rl-course/unit0/introduction)

![[Attachments/3babaa49bdb0f7c8c4dc04d9b0f8b84b_MD5.jpg]]

---

4. 计算机视觉课程

这门课程涵盖了目标检测、图像分割和图像分类。

所有内容都由 HuggingFace 模型提供支持。

[huggingface.co/learn/computer…](https://huggingface.co/learn/computer-vision-course/unit0/welcome/welcome)

![[Attachments/780644bcc42150175a5e039a7caa77c3_MD5.jpg]]

---

5. 音频课程

将声音转化为信号。

学习如何将 Transformers 应用于音频，例如语音识别、音乐标签或语音合成。

[huggingface.co/learn/audio-co…](https://huggingface.co/learn/audio-course/chapter0/introduction)

![[Attachments/584c0a8d9904fcf1d6917a4e88df319a_MD5.jpg]]

---

6. 游戏中的机器学习课程

AI 正在改变游戏的构建和玩法方式。

这门课程探索从 NPC 行为到程序化内容生成的所有内容。

[huggingface.co/learn/ml-games…](https://huggingface.co/learn/ml-games-course/unit0/introduction)

![[Attachments/102839ad7be9bdab5467023009b3818e_MD5.jpg]]

---

7. 3D 机器学习课程

处理 3D 数据，如点云或网格？

本课程涵盖了 3D 图形与机器学习的交集。

[huggingface.co/learn/ml-for-3…](https://huggingface.co/learn/ml-for-3d-course/unit0/introduction)

![[Attachments/1a6eba02e941821c08655957f222f742_MD5.jpg]]

---

8. 扩散模型课程

这项技术与 DALL·E 和 Stable Diffusion 相同。

你将一步步学习如何从噪声中生成图像。

[huggingface.co/learn/diffusio…](https://huggingface.co/learn/diffusion-course/unit0/1)

![[Attachments/25f0b5f8533f918102f4d85e3d47279f_MD5.jpg]]

---

9. 开源 AI 食谱

这不是课程，而是来自现实世界 AI 构建者的不断增长的笔记本库。

用它通过实践学习，克隆有效代码，或者更快速地构建。

[huggingface.co/learn/cookbook…](https://huggingface.co/learn/cookbook/index)

![[Attachments/90e388fd5f4e591b2900807bed4b8dad_MD5.jpg]]

---

以上就是全部，原作者 [@aigleeson](https://twitter.com/aigleeson)

如果您喜欢这个主题：

1.关注我（[@FinanceYF5](https://twitter.com/FinanceYF5)）  
2. 点赞+转发下面第一条帖子

[x.com/FinanceYF5/sta…](https://x.com/FinanceYF5/status/1936604491123671047)
