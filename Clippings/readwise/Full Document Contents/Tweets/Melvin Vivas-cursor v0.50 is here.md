---
人员: 
  - "[[<PERSON>]]"
tags:
  - tweets
日期: 2025-05-10
时间: None
链接: https://twitter.com/donvito/status/1921137235040366605
附件: https://pbs.twimg.com/profile_images/1833137601207009280/gGSDe5DF.jpg)
---
## Document Note

## Summary

cursor v0.50 is here

lots of juicy features

here's all the features you need to know 🧵 👇

## Full Document
cursor v0.50 is here

lots of juicy features

here's all the features you need to know 🧵 👇

![[Attachments/5d66e758ca9d61ee1851efb2fa4e2ef6_MD5.jpg]]

---

Simpler, unified pricing for [[@cursor\_ai](https://twitter.com/cursor_ai)](https://twitter.com/cursor_ai)

– All model usage now on request-based pricing  
– Max Mode adopts token-based pricing (like model APIs)  
– Premium tool calls & long context mode removed

---

Max Mode for all top models (token-based)

max mode is ideal for harder problems which require more context, intelligence and tool use

[@cursor\_ai](https://twitter.com/cursor_ai)

Your browser does not support the video tag.

---

Background Agent (Preview)

"a remote, asynchronous agent that runs outside the main Cursor editor in a remote containerized environment. It allows you to offload long-running or complex task"

works best for tasks that require less human interaction like fixing simple bugs, small features, or long changes

more information about it here  
<https://t.co/pqQtCuTNR9>

@cursor\_ai

Your browser does not support the video tag.

---

Include your entire codebase in context

use @ folders to add your entire codebase into context

[@cursor\_ai](https://twitter.com/cursor_ai)

Your browser does not support the video tag.

---

Inline Edit(Cmd/Ctrl+K) Refresh

Supports full file edits. Full file edits makes it easy to do scope changes to a file without using agent

You can also send the code block to the agent for multi-file edits

[@cursor\_ai](https://twitter.com/cursor_ai)

Your browser does not support the video tag.

---

Fast edits for long files with Agent

Agent can now find the exact place where edits should occur and change only that part

[@cursor\_ai](https://twitter.com/cursor_ai)

![[Attachments/f13300a2761dc6f19784a697339e9025_MD5.jpg]]

---

Workspaces

Work across multiple codebases in one session

![[Attachments/83b19751d8dac2bb7c411e73ee1a0903_MD5.jpg]]

---

Exporting Chat

You can now export chats to a markdown file. This is useful when you want to get advise from other AI about your code using the conversation you already have with [[@cursor\_ai](https://twitter.com/cursor_ai)](https://twitter.com/cursor_ai)

Your browser does not support the video tag.

---

Duplicate Chat

Useful when you want to fork a chat and explore different options

[@cursor\_ai](https://twitter.com/cursor_ai)

Your browser does not support the video tag.

---

like this update?

support me by reposting this thread 👇

you can also follow me [@donvito](https://twitter.com/donvito) for more AI

---

[x.com/donvito/status…](https://x.com/donvito/status/1921137235040366605)
