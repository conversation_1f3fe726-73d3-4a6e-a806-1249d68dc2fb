一早起来看见有佬友在推，刚好手头有个正在做的项目，于是就做了个简单的测试。

省流：各方面能力全面吊打trae，能力持平augment、cursor，强于windsurf，可控性方面吊打上述所有ide或者工具。

缺点：没有内置lsp做lint check，只能通过外部工具进行lint，比如通过总端执行 cargo check 进行代码检查。然后总端的集成有点问题，命令执行完成了，但是结果不一定拿得到，我通常直接手动复制结果给它也能解决。

测试例子：我的一个三通代理项目，目前日志系统尚不完善，只是做了基础的信息记录，便于我查看而已，但是控制台有上下文截断，内容一多不好排查proxy时候发生的一些错误。于是让他制作一个较为完善的日志系统。

我采用的是spec模式，也就是先规划，再编码，可控性极高。

首先，它会先针对你的项目进行分析，生成一个requirements，这个requirements会基于user case进行分析，这是业内首创（当然，指的是AI Coding领域，而非工程领域），非常具有创新。

下面是一个例子：

```
# 需求文档

## 介绍

此功能为聊天API系统添加全面的日志记录和持久化功能，以捕获所有请求/响应数据流，用于调试和故障排除。系统将存储输入请求、数据转换、原始AI服务响应和最终转换响应，以便对聊天处理管道进行详细分析。

## 需求

### 需求1

**用户故事：** 作为开发人员，我希望持久化所有传入的聊天请求，以便调试请求处理和数据转换问题。

#### 验收标准

1. 当收到聊天请求时，系统应使用唯一标识符存储原始请求数据
2. 当处理OpenAI聊天请求时，系统应保存请求负载、头部和元数据
3. 当处理Anthropic聊天请求时，系统应保存请求负载、头部和元数据
4. 当存储请求数据时，系统应包含时间戳、请求ID和客户端信息

### 需求2

**用户故事：** 作为开发人员，我希望跟踪不同AI提供商格式之间的数据转换，以便验证转换准确性。

#### 验收标准

1. 当Anthropic请求转换为OpenAI格式时，系统应存储原始和转换后的请求数据
2. 当响应数据在格式之间转换时，系统应存储源格式和目标格式数据
3. 当发生数据转换时，系统应使用请求ID将转换后的数据链接到原始请求
4. 当转换失败时，系统应记录错误详情和部分转换状态

### 需求3

**用户故事：** 作为开发人员，我希望捕获原始AI服务响应，以便分析响应模式和调试解析问题。

#### 验收标准

1. 当AI服务返回响应时，系统应存储完整的原始响应数据
2. 当接收到流式响应时，系统应捕获所有流块并重构完整响应
3. 当接收到非流式响应时，系统应存储完整的JSON响应负载
4. 当AI服务发生错误时，系统应存储错误响应和状态码

### 需求4

**用户故事：** 作为开发人员，我希望持久化最终处理的响应，以便验证完整的请求-响应周期。

#### 验收标准

1. 当响应成功处理时，系统应存储发送给客户端的最终响应
2. 当响应转换完成时，系统应将最终响应链接到原始请求和原始响应
3. 当发送流式响应时，系统应捕获完整的流输出
4. 当响应处理失败时，系统应存储错误状态和部分响应数据

### 需求5

**用户故事：** 作为开发人员，我希望高效存储和检索日志数据，以便日志系统不会影响API性能。

#### 验收标准

1. 当执行日志操作时，系统应使用异步处理以避免阻塞请求处理
2. 当存储操作失败时，系统应继续处理请求而不中断
3. 当存储大量响应数据时，系统应实现高效的序列化和压缩
4. 当日志数据积累时，系统应提供可配置的保留策略

### 需求6

**用户故事：** 作为开发人员，我希望查询和分析日志数据，以便排查特定问题并识别模式。

#### 验收标准

1. 当查询日志数据时，系统应支持按请求ID、时间范围和提供商类型过滤
2. 当分析请求流时，系统应提供从输入到输出的完整可追溯性
3. 当调查错误时，系统应允许将失败的请求与其处理步骤关联
4. 当审查性能时，系统应包含每个处理阶段的时间信息

```

从上面这个需求文档我们可以看出，kiro对需求的边界进行了较为清晰的划分，对验收标准进行了完整的定义，做过开发的朋友应该知道，无论你怎么实现，其实最重要的就是通过最后的验收阶段就行了，不然就算你代码写得再好，验收过不了，money免谈。

接着，他根据需求分析进行design，这一步主要是做实现的规划，特别是一些结构之类的进行较为详细的阐述，这部分内容较多我就不直接放出来了。并且这一步表现中规中矩，不过好在他的产物是可以编辑的，我手动修改了部分内容的实现路径。

再接着会基于实现设计阶段的内容，进行任务的拆分，将其细化从一个个task较为独立的task，且每个task都会指出其实现的对应的需求点是什么，非常清晰直观。

```
# 实施计划

- [ ] 1. 建立日志基础设施和核心类型
  - 创建具有核心类型和枚举的日志模块结构
  - 定义LogEvent枚举，包含用于请求生命周期跟踪的所有事件变体
  - 使用thiserror实现LoggingError枚举，提供适当的错误处理
  - 创建LoggingConfig结构体，包含全面的配置选项
  - _需求: 5.1, 5.2, 5.3_

- [ ] 2. 实现数据库模式和实体
  - 为chat_request_logs表创建SeaORM实体模型
  - 为chat_transformation_logs表创建SeaORM实体模型
  - 为chat_ai_service_logs表创建SeaORM实体模型
  - 为所有日志表编写数据库迁移文件
  - _需求: 1.1, 1.4, 2.3, 3.1_

- [ ] 3. 创建文件存储系统
  - 实现具有基于日期的目录结构的文件存储工具
  - 添加使用gzip的JSON压缩和解压缩功能
  - 创建具有关联ID组织的文件路径生成逻辑
  - 编写文件清理和保留管理功能
  - _需求: 5.3, 6.4_

- [ ] 4. 构建核心日志服务
  - 实现具有基于异步通道的事件处理的ChatLogService结构体
  - 创建处理不同LogEvent类型的日志事件处理器
  - 添加结构化元数据的数据库持久化逻辑
  - 实现带压缩的大负载文件存储逻辑
  - 编写具有回退策略的错误恢复机制
  - _需求: 5.1, 5.2, 5.4_

- [ ] 5. 创建日志数据模型
  - 实现具有提供商无关请求表示的LoggedRequest结构体
  - 创建支持完整、流式和错误响应的LoggedResponse枚举
  - 添加RequestParameters和ResponseMetadata结构体，用于全面的数据捕获
  - 为所有日志数据类型编写序列化和反序列化逻辑
  - _需求: 1.1, 1.2, 3.1, 3.2, 4.1_

- [ ] 6. 实现请求日志集成
  - 使用ULID创建关联ID生成，用于唯一请求跟踪
  - 向OpenAI聊天处理函数添加请求日志调用
  - 向Anthropic聊天处理函数添加请求日志调用
  - 实现敏感数据的头部提取和清理
  - _需求: 1.1, 1.2, 1.3, 1.4_

- [ ] 7. 添加转换日志
  - 在Anthropic到OpenAI转换中实现转换日志
  - 在OpenAI到Anthropic响应转换中添加转换日志
  - 为所有格式转换创建转换前后数据捕获
  - 使用关联ID将转换日志链接到原始请求
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [ ] 8. 集成AI服务响应日志
  - 在call_ai_service函数中添加原始响应日志
  - 实现流式响应重构以进行完整日志记录
  - 创建响应状态和时间捕获
  - 添加带有详细错误上下文的错误响应日志
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [ ] 9. 实现最终响应日志
  - 在handle_non_streaming_response中添加最终响应日志
  - 在handle_anthropic_non_streaming_response中添加最终响应日志
  - 在handle_streaming_response中实现流式响应日志
  - 在handle_anthropic_streaming_response中添加流式响应日志
  - 将最终响应链接到原始请求和原始响应
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [ ] 10. 创建日志服务初始化
  - 向主应用程序启动添加日志服务初始化
  - 实现从环境变量和配置文件加载配置
  - 为日志服务创建数据库连接池设置
  - 为日志服务添加优雅关闭处理
  - _需求: 5.1, 5.2_

- [ ] 11. 添加查询和分析功能
  - 创建按关联ID和时间戳过滤日志的查询功能
  - 实现带有提供商类型过滤的日志检索功能
  - 添加用于排查失败请求的错误关联功能
  - 创建带有时间信息的性能分析功能
  - _需求: 6.1, 6.2, 6.3, 6.4_

- [ ] 12. 编写全面的测试
  - 为所有日志数据结构和序列化创建单元测试
  - 为完整的请求-响应日志周期编写集成测试
  - 添加日志故障恢复的错误场景测试
  - 实现性能测试以测量日志开销影响
  - 创建用于模拟数据生成和清理的测试工具
  - _需求: 5.2, 5.4_
```

完成上述spec部分，开始进入后续的真实开发阶段。由于前面的设计已经十分完善了，这一个步骤主要面临的问题就是代码写错遇到编译错误，解决便于错误罢了。而且得力于rust的编译器，很清晰的指出了对应的问题所在，加上claude 4的开发能力，基本上没啥难度就能解决了。

另外，还有一点做的非常好的。  

[![image](https://linux.do/uploads/default/optimized/4X/f/6/1/f6131de147cfa779400157154a0ab31b0c2b1867_2_626x500.png)](https://linux.do/uploads/default/original/4X/f/6/1/f6131de147cfa779400157154a0ab31b0c2b1867.png "image")

他会在coding的过程中，实时的去修改你的task状态，而且更进一步的，它甚至为你做了可视化的工作，为你将每个task做的changes都做了划分，你可以很清晰的去查看每个task的变更。

结果：  
![image](https://linux.do/uploads/default/original/4X/b/d/3/bd3be615d8be550deb02577f89c09ed50f1924a5.png)

结语：AI Coding这个领域，是创新者的领域，而不是抄袭者的领域，一味的copy，搞不清楚自己产品的定位，怎么可能会有人用呢。作为和augument同为后来者的kiro，和augment一样，达到了属于它自己的moment。