## 前言废话

Augment超上下文、记忆整合、项目结构理解、终端执行这一块真的越来越舒服了，甚至能一口气把几十章短篇小说生成，你还能顺便发到番茄🍅赚点广告费~  
抱歉~Curosr请走开，我怕Augment误会  
现在对我来说小项目Curosr，大项目Augment完全可行！

## 直捣黄龙

### 设备唯一标识（SessionId类）

在所有接口中都有一个 `SessionId` 他的来源很简单

```
// 在SessionId类中发现的关键代码public static String get() {    // 1. 优先使用IntelliJ的永久安装ID    String permanentId = PermanentInstallationID.get();    if (permanentId != null) {        return permanentId;    }    // 2. 备用方案：生成UUID并持久化    return generateAndStoreUUID();}
```

### 系统环境收集（SystemEnvironment类）

在我一天被封了七八个号之后，我决定重新开始解刨这个该死的Aug（真的是**又爱又恨**啊，你便宜一点我不就买了嘛，啊啊啊！当个牛马还要自费买鞭子），一路追，追到请求接口的时候发现了这个神奇的类，并且里面的内容也被打包放到请求context中，这可能是修改 `SessionId` 之后依然被风控的原因之一？？

```
// SystemEnvironment类收集的信息public class SystemEnvironment {    public Map<String, String> getenv();     // 系统环境变量    public String getProperty(String key);   // 系统属性}
```

这个环境变量里面大致包含了这些东西：

- • 操作系统类型和版本
    
- • Java运行时版本
    
- • IDE版本和构建信息
    
- • 系统架构（x86/x64/ARM）
    
- • 内存和CPU信息
    
- • 网络配置信息
    

### Sentry监控系统（SentryMetadataCollector类）

我发现了一个神奇的类，他居然会在登录成功之后上报这些东西

```
public class SentryMetadataCollector {    // 收集系统标签    private Object collectSystemTags();        // 收集内存指标    private MemoryMetrics collectMemoryMetrics();        // 收集仓库指标    private Object collectRepositoryMetrics();        // 统计Git跟踪文件    public Object countGitTrackedFiles();}
```

我看了一下，大致包含下面这些：

- • 系统信息：os类型、版本（例：win11 24h）、架构（amd、arm）
    
- • 内存指标：堆内存使用、GC统计、垃圾回收（收集插件）
    
- • 性能指标：接口响应时间、错误次数等
    
- • 环境信息：跟上面环境系统变量差不多
    
- • 用户标识：用户ID之类的
    
- • Git信息：仓库根目录、当前分支信息、提交历史摘要、文件变更、**Git配置信息**、远程地址URL
    

其实到了这里大家也就大致清楚了，Augment 为什么这么容易精准风控了吧，这么多电脑的敏感信息都被他一股脑传到后端去，这不是相对于把你的户口本+实时GPS定位告诉别人？ = =

## 清理（工具）

能做到以下效果：

- • 🆕 **重新生成SessionID**
    
- • 🔄 **重置所有跟踪数据**
    
- • 🎯 **清除用户行为记录**
    
- • 🧹 **删除缓存和日志**
    
- • 🔒 **断开与之前会话的关联**
    

工具下载：  
https://github.com/yuaotian/go-augment-cleaner/

PS：点个星星吧~

## 结束（踹门！下班）

解剖之后我才真正感到绝望，呵呵呵(¯ㅿ¯;)，蒜鸟~ 蒜鸟~ 能用一天是一天吧~