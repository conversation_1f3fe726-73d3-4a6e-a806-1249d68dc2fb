## 前言

最近没去玩Cursor，脚本也好久没更新，提示词（4.8版本）也没怎么更新，主要是对Augment感兴趣，在Idea上不要太舒服了，所以对这个ACE (Augment Context Engine)，产生了兴趣，想去深入了解学习，也为了更好写代码（~~bug~~)当牛马，下面大部分内容是自写+少量AI润色（代码块、流程图、表述不清楚的地方等），顺便拿出来水一贴。

下面是我对 `intellij-augment-0.221.1.jar` 包解析出来的原理解答和实际工作经验中探索出来的，所以有耐心的话慢慢看，没耐心可以跳到最下面（高级技巧、高效触发ACE）的部分，稍微学一下，注意怎么高效使用即可。

## ACE (Augment Context Engine) 触发机制

### 1、ACE触发条件解析

#### 1.1、ACE自动触发

自动触发场景：

- • 1.Agent模式激活：当mode设置为"AGENT"时
    
- • 2.文件上下文存在：当前有打开的文件或选中代码
    
- • 3.用户指定文件：通过`@文件名`引用特定文件
    
- • 4.复杂查询检测：AI检测到需要代码库级别理解的查询
    

```
public class ChatRequest {    public String mode;                     // "AGENT" 模式自动触发ACE    public BlobsPayload blobs;              // 有文件上下文时触发    public List<String> userGuidedBlobs;    // 用户指定文件时强制触发    public String contextCodeExchangeRequestId; // 上下文代码交换时触发}
```

#### 1.2、手动触发

```
public class AgentCodebaseRetrievalRequest {    public String informationRequest;       // 信息请求描述    public BlobsPayload blobs;             // 相关文件blob    public List<Exchange> chatHistory;      // 聊天历史上下文    public Integer maxOutputLength;        // 最大输出长度}
```

### 2、Augment的意图识别系统

#### 2.1、查询类型分类

基于分析，ACE能识别以下查询类型：

**代码理解类查询：**

- • "这个函数是做什么的？"
    
- • "解释这段代码的逻辑"
    
- • "这个类的作用是什么？"
    

**代码搜索类查询：**

- • "找到所有使用Redis的地方"
    
- • "哪里定义了User类？"
    
- • "搜索所有的API接口"
    

**架构分析类查询：**

- • "这个项目的整体架构是什么？"
    
- • "模块之间的依赖关系"
    
- • "数据流是怎样的？"
    

**问题诊断类查询：**

- • "为什么这里会报错？"
    
- • "这个bug可能在哪里？"
    
- • "性能瓶颈在哪里？"
    

**代码生成类查询：**

- • "帮我写一个类似的函数"
    
- • "生成这个接口的实现"
    
- • "创建一个新的组件"
    

#### 2.2 上下文权重计算

```
// 基于FeatureDetectionFlags的分析publicclassContextWeightCalculator {    publicdoublecalculateWeight(String query, FileContext context) {        doubleweight=0.0;                // 关键词权重        if (containsCodeTerms(query)) weight += 0.3;        if (containsArchitectureTerms(query)) weight += 0.4;        if (containsDebuggingTerms(query)) weight += 0.5;                // 上下文相关性        if (hasSelectedCode(context)) weight += 0.2;        if (hasMultipleFiles(context)) weight += 0.3;        if (hasComplexProject(context)) weight += 0.4;                return Math.min(weight, 1.0);    }}
```

## 3、高效触发ACE

### 1. 触发ACE的关键词和短语

#### 1.1 强制触发词汇

**架构相关：**

- • "整体架构" / "overall architecture"
    
- • "系统设计" / "system design"
    
- • "模块关系" / "module relationships"
    
- • "依赖图" / "dependency graph"
    

**代码库级别：**

- • "在整个项目中" / "across the entire project"
    
- • "所有相关文件" / "all related files"
    
- • "项目范围内" / "project-wide"
    
- • "代码库搜索" / "codebase search"
    

**深度分析：**

- • "深入分析" / "deep analysis"
    
- • "详细解释" / "detailed explanation"
    
- • "完整理解" / "comprehensive understanding"
    
- • "全面检查" / "thorough examination"
    

#### 1.2 上下文增强词汇

**文件引用：**

- • "@文件名" - 直接引用特定文件
    
- • "相关文件" / "related files"
    
- • "依赖文件" / "dependent files"
    
- • "调用链" / "call chain"
    

**范围指定：**

- • "包括测试文件" / "including test files"
    
- • "配置文件" / "configuration files"
    
- • "所有模块" / "all modules"
    
- • "子项目" / "subprojects"
    

### 2. 提示词模板（参考）

#### 2.1 代码理解模板

```
模板1：深度代码分析"请深入分析[具体代码/文件/功能]的实现原理，包括：1. 核心逻辑和算法2. 与其他模块的交互3. 在整个项目中的作用4. 潜在的改进点"模板2：架构级别理解"从整体架构的角度，解释[功能/模块]是如何工作的：- 在系统中的位置- 与其他组件的关系- 数据流向和处理过程- 设计模式和最佳实践"
```

#### 2.2 问题诊断模板

模板3：Bug分析  
"帮我分析这个问题：[具体问题描述]  
请检查：

- • 相关的代码文件
    
- • 可能的错误原因
    
- • 类似的已知问题
    
- • 建议的解决方案"
    

模板4：性能分析  
"分析[功能/模块]的性能问题：

- • 识别性能瓶颈
    
- • 检查相关的配置
    
- • 对比最佳实践
    
- • 提供优化建议"
    

#### 2.3 代码生成模板

模板5：基于现有代码生成  
"基于项目中现有的[类似功能/模式]，帮我生成[新功能]：

- • 遵循项目的编码规范
    
- • 使用相同的设计模式
    
- • 保持一致的命名约定
    
- • 包含适当的错误处理"
    

模板6：接口实现  
"为[接口/抽象类]创建实现，参考项目中的其他实现：

- • 分析现有实现模式
    
- • 遵循项目架构
    
- • 包含必要的依赖注入
    
- • 添加适当的日志和监控"
    

## 实战示例

### 1. 触发ACE的有效查询示例

#### 示例1：架构分析查询

```
用户查询："请分析这个Spring Boot项目的整体架构，包括：1. 各层之间的关系（Controller、Service、Repository）2. 数据流向和处理过程3. 配置文件的作用4. 与数据库的交互方式"ACE响应特征：- 会扫描所有相关的Java文件- 分析Spring配置文件- 检查数据库配置和实体类- 生成架构图和说明
```

#### 示例2：代码搜索查询

```
用户查询：在整个项目中找到所有使用Redis的地方，包括： - 配置文件中的Redis设置 - 代码中的Redis操作 - 缓存注解的使用 - 相关的工具类"ACE响应特征： - 搜索所有Java文件中的Redis相关代码 - 检查配置文件（application.yml等） - 分析依赖文件（pom.xml等） - 提供完整的使用清单
```

#### 示例3：问题诊断查询

```
用户查询："这个接口响应很慢，帮我分析可能的原因：@GetMapping('/api/users')请检查相关的Service、Repository和数据库查询"ACE响应特征： - 分析Controller、Service、Repository的完整调用链 - 检查数据库查询和索引 - 分析缓存策略 - 提供性能优化建议
```

### 2. 无效查询的改进建议

#### 无效查询示例：

```
❌ "这个代码有什么问题？"改进为：✅ "分析这个UserService类的实现，检查是否有性能问题、安全漏洞或违反最佳实践的地方"❌ "帮我写个函数"改进为：✅ "基于项目中现有的数据访问模式，帮我为User实体创建一个CRUD服务类，包括适当的异常处理和事务管理"❌ "这个项目是做什么的？"改进为：✅ "通过分析项目结构、配置文件和主要类，解释这个应用的业务功能、技术架构和部署方式"
```

---

## 高级触发技巧 （仅供参考）

### 1. 利用Agent模式

> 分析整个项目的安全性问题

```
// 在ChatRequest中设置mode为"AGENT"{    "mode": "AGENT",    "message": "分析整个项目的安全性问题",    "blobs": {...},    "agent_memories": "..."}
```

### 2. 使用文件引用

```
直接引用："@UserController.java 这个控制器的安全性如何？""@application.yml 这个配置有什么问题？"批量引用："分析@UserService.java、@UserRepository.java和@User.java之间的关系"
```

### 3. 上下文链式查询

```
第一步：建立上下文"请分析这个电商项目的订单处理流程"第二步：深入细节"在订单处理流程中，库存管理是如何实现的？"第三步：问题诊断"库存管理中可能存在什么并发问题？"
```

## 📊 触发成功率

### 高成功率（90%+）

- • 包含"整个项目"、"所有文件"
    
- • 使用`@文件名`引用
    
- • Agent模式
    
- • 架构分析查询
    

### 中等成功率（60-90%）

- • 涉及多个文件的问题
    
- • 性能和安全查询
    
- • 代码重构建议
    

### 低成功率（<60%）

- • 简单语法问题
    
- • 单一文件小问题
    
- • 基础概念解释
    

## ACE 系统架构图

```
┌─────────────────────────────────────────────────────────────┐│                 Augment Context Engine                      │├─────────────────────────────────────────────────────────────┤│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ ││  │   本地索引系统    │  │  代码库检索系统   │  │  工作空间感知系统 │ ││  │ AugmentLocalIndex│  │AgentCodebaseRet │  │WorkspaceFolderI │ ││  │ AugmentBlobState │  │rieval System    │  │nfo System       │ ││  └─────────────────┘  └─────────────────┘  └─────────────────┘ ││  ┌─────────────────┐  ┌─────────────────┐                     ││  │    记忆系统      │  │   检查点系统     │                     ││  │ MemoriesService  │  │ Checkpoint      │                     ││  │                 │  │ System          │                     ││  └─────────────────┘  └─────────────────┘                     │└─────────────────────────────────────────────────────────────┘                              │                              ▼┌─────────────────────────────────────────────────────────────┐│                    IntelliJ Platform                       ││              FileBasedIndex Infrastructure                  │└─────────────────────────────────────────────────────────────┘
```

```
全局记忆 (Global Memory)├── 用户偏好记忆 (User Preferences)│   ├── 编程风格偏好│   ├── 常用模式和习惯│   └── 错误修正历史├── 项目特定记忆 (Project-Specific Memory)  │   ├── 项目架构理解│   ├── 业务逻辑映射│   └── 团队编程规范└── 会话记忆 (Session Memory)    ├── 当前对话上下文    ├── 临时学习内容    └── 实时反馈信息
```

PS：`Augment`的ACE会把他建立全部记忆、代码索引全部上传云端的！！！所以如果建议的请慎重考虑是否使用~

---

好了，这些就是我这些天使用`Augment`的小小技巧了，你已经成功学会如何正确高效的使用ACE (Augment Context Engine) 这个强大的 `工具` 了，Cursor真的要好好学习学习人家`Augment`~

版本：intellij-augment-0.221.1.jar