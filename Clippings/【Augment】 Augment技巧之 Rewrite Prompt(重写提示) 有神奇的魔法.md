> 字数 2503，阅读大约需 5 分钟，随便看看约 1 分钟~

## 🔥AI助手总是理解错你的意思？这个隐藏功能让准确率暴增60%！

## 前言

说真的，augment在某体验上面已经强过Cursor，那就是Rewrite Prompt（重写提示）把非结构化自然语言输入转换为更适合 AI 模型理解的结构化格式。

人话：把你口语化（非结构化）的文字，根据窗口上下文聊天记录和代码索引库对你的输入内容进行针对性优化。

## 三大核心

### 1. 提示标准化

- • 将用户的非结构化自然语言输入转换为更适合 AI 模型理解的结构化格式
    
- • 统一不同用户输入风格，减少模型理解的歧义性
    

### 2. 上下文增强

- • 基于项目上下文和历史对话，为用户输入添加必要的背景信息
    
- • 结合代码库知识，提供更精确的技术术语和概念
    

### 3. 内容分类和提炼

- • 自动识别用户输入的意图类型（如代码生成、问题解答、重构建议等）
    
- • 根据不同类型应用相应的优化策略
    

## 示例

### 1️⃣ 提示标准化

**你的原始输入：**

```
帮我写个函数
```

**AI收到的实际指令：**

```
作为一个专业的软件开发助手，请基于以下用户输入提供准确的技术建议：用户问题：帮我写个函数请在回答时考虑：1. 代码的可读性和维护性2. 性能优化的可能性3. 安全性最佳实践4. 与现有代码库的兼容性请提供具体的代码示例和详细的解释。
```

看到区别了吗？你随口一句话，AI接收到的却是一个结构化、专业化的技术需求！这就像是给AI配了个"翻译官"——把你的大白话翻译成AI最容易理解的"专业术语"。

### 2️⃣ 上下文增强

它还能"读懂"你的项目环境。就像一个新来的同事，不仅要听懂你的需求，还要了解公司的技术栈、编码规范、项目背景...

**看个实际例子：**

你问："如何处理数据库连接？"  
普通AI可能会给你一堆通用的数据库连接教程。  
但经过上下文增强后，AI收到的是：

```
基于当前 Java Spring Boot 项目上下文，请分析以下需求：如何处理数据库连接？项目背景： - 使用 Spring Boot 2.7+ 框架 - 集成 JPA 和 MySQL 数据库 - 遵循 RESTful API 设计规范 - 采用分层架构模式请提供符合项目架构的解决方案...
```

这样AI就能给出针对你具体项目的专业建议，而不是泛泛而谈的通用方案！

### 3️⃣ 内容分类提炼

这是最智能的部分——AI能自动识别你的意图类型！  
是想生成代码？还是修复bug？或者是性能优化？不同的需求，AI会采用不同的"回答策略"。  
**分类逻辑示例：**

```
// 智能分类机制（简化版）if (userInput.contains("写") || userInput.contains("生成")) {    // 代码生成模式：强调完整性和规范性    return codeGenerationTemplate;} else if (userInput.contains("错误") || userInput.contains("bug")) {    // 问题诊断模式：重点分析原因和解决方案      return bugFixingTemplate;}
```

**场景1 - 代码生成：**

- • 你说："写个排序算法"
    
- • AI理解为："代码生成请求，需要提供完整可运行的代码，包含注释和错误处理"
    

**场景2 - 问题诊断：**

- • 你说："程序报NullPointerException"
    
- • AI理解为："问题诊断请求，需要分析根本原因，提供修复步骤和预防措施"
    

这就像是给AI装了个"读心术"——它能根据你的话判断你真正想要什么！

## 源码剖析

> 重写提示词的核心代码部分，其实没啥好讲，但是我喜欢收集放在文章里面，不喜欢可以跳过~

#### **核心内容：**

- • `FeatureFlagManager.promptEnhancerEnabled()` 方法详解
    
- • `AugmentWebviewChatServiceImpl.applyClassifyAndDistillPrompt()` 核心实现
    
- • `ChatInitializeResponseData` 配置字段分析
    
- • `memoriesParams` 和 `classify_and_distill_prompt` 配置机制
    
- • 数据流转和处理逻辑
    

### AugmentWebviewChatServiceImpl.applyClassifyAndDistillPrompt() 方法

#### 方法签名和核心实现

```
// 文件: com/augmentcode/intellij/chat/AugmentWebviewChatServiceImpl.javaprivatefinalbooleanapplyClassifyAndDistillPrompt(    ChatUserMessageRequest request,     ChatRequest chatRequest) {    // 1. 获取特性标志管理器    FeatureFlagManagerfeatureFlagManager= FeatureFlagManager.Companion.getInstance();        // 2. 获取记忆参数配置    StringmemoriesParams= featureFlagManager.memoriesParams(true);    JsonObjectmemoriesParamsJson= (JsonObject)(newGson()).fromJson(memoriesParams, JsonObject.class);        // 3. 提取分类和提炼提示模板    JsonElementvar10000= memoriesParamsJson.get("classify_and_distill_prompt");    StringclassifyAndDistillPrompt= var10000 != null ? var10000.getAsString() : null;        // 4. 验证模板有效性    if (classifyAndDistillPrompt == null || classifyAndDistillPrompt.length() == 0) {        logger.error("Classify and distill prompt missing.");        returnfalse;    }        // 5. 应用模板到主消息    StringuserText= request.getData().getText();    chatRequest.message = StringsKt.replace$default(        classifyAndDistillPrompt,         "{message}",         userText,         false, 4, (Object)null    );        // 6. 处理节点内容（如果存在）    if (request.getData().getNodesCount() > 0) {        // 遍历所有节点，对文本节点应用重写        for (ChatRequestNode node : request.getData().getNodesList()) {            if (node.getType() == ChatRequestNodeType.TEXT.getNumber() && node.hasTextNode()) {                // 重写文本节点内容                ChatRequestTextupdatedTextNode= node.getTextNode().toBuilder()                    .setContent(StringsKt.replace$default(                        classifyAndDistillPrompt,                         "{message}",                         node.getTextNode().getContent(),                         false, 4, (Object)null                    ))                    .build();                                // 更新节点                ChatRequestNodeupdatedNode= node.toBuilder()                    .setTextNode(updatedTextNode)                    .build();            }        }    }        returntrue;}
```

```
// 核心处理流程publicbooleanenhancePrompt(ChatUserMessageRequest request, ChatRequest chatRequest) {    try {        // 1. 获取优化模板        Stringtemplate= getEnhancementTemplate();                // 2. 提取用户输入        StringuserInput= request.getData().getText();                // 3. 模板替换        StringenhancedPrompt= template.replace("{message}", userInput);                // 4. 更新请求        chatRequest.message = enhancedPrompt;                returntrue;    } catch (Exception e) {        // 异常时使用原始输入，确保功能稳定性        returnfalse;    }}// 配置更新不阻塞主流程@Asyncpublic CompletableFuture<Void> updateConfigurationAsync() {    return CompletableFuture.runAsync(() -> {        // 后台更新配置，用户无感知        updateLatestConfiguration();    });}
```

## 实际应用示例

### 示例1：Java 开发场景

**用户原始输入：**

```
我的 Spring Boot 应用启动很慢，怎么优化？
```

**系统处理过程：**

1. 1. **特性标志检查**：`promptEnhancerEnabled(true)` → `true`
    
2. 2. **配置获取**：从 `memoriesParams` 获取优化模板
    
3. 3. **意图分类**：识别为"性能优化"类型
    
4. 4. **模板应用**：使用性能优化专用模板
    

**最终重写结果：**

```
基于 Spring Boot 项目的性能优化分析请求：我的 Spring Boot 应用启动很慢，怎么优化？项目上下文：- Spring Boot 2.7+ 框架环境- JVM 性能调优需求- 生产环境部署考虑请提供系统性的优化方案：1. 启动时间瓶颈分析方法2. 具体的配置优化建议3. JVM 参数调优策略4. 监控和测试验证方法5. 预期性能提升效果评估请包含可执行的优化步骤和相关代码示例。
```

### 重写前后对比

|方面|重写前|重写后|
|---|---|---|
|**明确性**|模糊的自然语言|结构化的技术需求|
|**上下文**|缺乏背景信息|包含项目和技术上下文|
|**质量要求**|未明确标准|明确的质量和规范要求|
|**输出格式**|随机格式|标准化的响应结构|

## 功能在这~

![图片](data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='1px' height='1px' viewBox='0 0 1 1' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' fill-opacity='0'%3E%3Cg transform='translate(-249.000000, -126.000000)' fill='%23FFFFFF'%3E%3Crect x='249' y='126' width='1' height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)

## 🤔 写在最后

Rewrite Prompt 技术的魅力就在于此——看似复杂的问题，往往有着优雅简洁的解决方案。  
最关键的是，这一切都是**透明的**——你还是用原来的方式跟AI对话，但得到的却是质量提升60%的专业回答！  
一个 Rewrite Prompt 就能让AI的理解能力提升一个档次。这种"四两拨千斤"的设计思路，真的值得我们(~~Curosr~~)学习。 :tieba_025:

不是我说，虽然类似的Cursor也有，那就是在git上面也有一个，但是人家augment直接移到用户输入框，还结合自己代码库和窗口上下文一起，虽然有时候其实生成的不太符合或者不是很完美，但也八九不离十，自己改一改新的提示词，再不济拿来参考一下，简简单单就能让AI翻倍提升理解，何乐不为呢。

小小吐槽一下：最近Cursor没怎么更新功能，都花心思花在Pro定价和使用规则上面。服了(*￣︿￣)

![图片](data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='1px' height='1px' viewBox='0 0 1 1' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' fill-opacity='0'%3E%3Cg transform='translate(-249.000000, -126.000000)' fill='%23FFFFFF'%3E%3Crect x='249' y='126' width='1' height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)