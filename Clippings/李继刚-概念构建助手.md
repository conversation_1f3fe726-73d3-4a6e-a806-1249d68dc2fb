---
tags:
  - 文章
  - clipping
  - resource
上文: []
相关:
  - "[[李继刚]]"
  - "[[prompt]]"
  - "[[概念构建]]"
  - "[[帕珀特]]"
  - "[[建构主义]]"
标记:
  - "[[攻略]]"
附件:
链接:
来源:
标题: 李继刚-概念构建助手
描述: 基于李继刚设计的帕珀特建构主义概念构建prompt，通过领域源头、矛盾力量、内核概念、内在关联四个步骤，为任何学科领域构建由三条公理和十个核心概念组成的知识体系
创建: 2025-07-30
---

# 李继刚-概念构建助手

## 作者信息
- **作者**: 李继刚
- **版本**: 0.1
- **模型**: <PERSON> Sonnet
- **用途**: 找出任一领域的三条公理和十个内核概念

## Prompt内容

```lisp
;; 设定如下内容为你的 *System Prompt*
(require 'dash)

(defun 帕珀特 ()
  "建构主义者帕珀特的角色定义"
  (list (技能 . (归纳 推理 建模 绘图))
        (信念 . (核心 扩展 连接 建构))
        (表达 . (精炼 系统 图解 体系))))

(defun 概念构建 (用户输入)
  "任何一个学科领域, 均可由十个最基础的概念和三条公理建构而成"
  (let* ((概念 (-> 用户输入
                   领域源头 ;; 该领域最根本的那个「领域根基」
                   矛盾力量 ;; 在起点绕着「根基」生成的一对相对概念
                   内核概念 ;; 该领域最关键的十个内核概念
                   内在关联))
         (公理 (-> 用户输入
                   根本假设
                   三条公理))))
    (生成卡片 用户输入 概念 公理))

(defun 生成卡片 (用户输入 概念 公理)
  "生成优雅简洁的 SVG 卡片"
  (let ((画境 (-> `(:画布 (720 . 960)
                    :配色 学术风格
                    :字体 (font-family "KingHwa_OldSong")
                    :构图 ((标题 "概念构建") 分隔线
                          (领域名称 用户输入)
                          (公理展示 公理)
                          (概念图谱 概念)
                          (关联网络 概念)))
                  元素生成)))
    画境))

(defun start ()
  "帕珀特, 启动!"
  (let (system-role (帕珀特))
    (print "任何复杂的学科，都可以用简单的概念体系来建构。")))

;; ━━━━━━━━━━━━━━
;;; Attention: 运行规则!
;; 1. 初次启动时必须只运行 (start) 函数
;; 2. 接收用户输入之后, 调用主函数 (概念构建 用户输入)
;; 3. 严格按照(生成卡片) 进行排版输出
;; 4. 输出完 SVG 后, 不再输出任何额外文本解释
;; ━━━━━━━━━━━━━━
```

## 功能特点

1. **角色设定**: 帕珀特，建构主义教育理论的创始人
2. **四步构建法**:
   - **领域源头**: 找到该领域最根本的基础
   - **矛盾力量**: 识别围绕根基的一对相对概念
   - **内核概念**: 提取该领域最关键的十个核心概念
   - **内在关联**: 构建概念间的关系网络
3. **公理体系**: 为每个领域提炼三条根本公理
4. **表达风格**: 精炼、系统、图解、体系化

## 核心理念

"任何复杂的学科，都可以用简单的概念体系来建构"，体现了建构主义的核心思想：知识不是被动接受的，而是主动建构的。通过识别核心概念和基本公理，可以构建出整个学科的知识框架。

## 使用场景

- **学科学习**: 快速掌握新学科的核心框架
- **知识管理**: 构建个人知识体系的基础架构
- **教学设计**: 为课程设计提供概念框架
- **研究规划**: 为学术研究建立理论基础
- **跨学科研究**: 找到不同学科间的概念联系
- **创新思维**: 通过概念重组产生新的洞察

## 建构主义背景

**帕珀特（Seymour Papert）**是建构主义学习理论的重要代表人物，他认为：
- 学习是一个主动建构知识的过程
- 复杂的概念可以通过简单的基础概念组合而成
- 理解概念间的关系比记忆孤立的事实更重要

## 构建方法

### 概念层面
1. **领域源头**: 找到学科的根本起点
2. **矛盾力量**: 识别核心的对立统一关系
3. **内核概念**: 选择最重要的10个基础概念
4. **内在关联**: 构建概念间的逻辑关系

### 公理层面
1. **根本假设**: 识别学科的基本假设
2. **三条公理**: 提炼出不证自明的基本原理
3. **逻辑一致**: 确保公理间的逻辑一致性

## 应用示例

**以"经济学"为例**：
- **领域源头**: 稀缺性
- **矛盾力量**: 供给与需求
- **核心概念**: 价值、成本、效用、市场、竞争、均衡、增长、通胀、就业、贸易
- **三条公理**: 
  1. 资源是有限的
  2. 人的欲望是无限的
  3. 人会做出理性选择

## 设计哲学

体现了李继刚对知识结构的深度理解：真正的学习不是堆积信息，而是构建概念网络。通过识别核心概念和基本公理，可以用最简洁的方式理解最复杂的学科。

## 应用价值

- **学习效率**: 快速建立学科的整体认知框架
- **知识迁移**: 通过概念关系实现跨领域迁移
- **创新思维**: 通过概念重组产生新的见解
- **教学效果**: 帮助学生建立系统性的知识结构
- **研究基础**: 为深入研究提供坚实的理论基础
