<!doctype html>
<html lang="ja">
<head>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>出入国在留管理庁ホームページ</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
<meta name="format-detection" content="telephone=no">
<meta name="description" content="平成31年4月1日から、入国管理局は「出入国在留管理庁」となりました。当庁に関する業務については、引き続き、「出入国在留管理庁ホームページ」において情報提供いたします。">
<meta name="keywords" content="">
<link rel="shortcut icon" href="/isa/content/001361132.png">
<link rel="stylesheet" type="text/css" href="/isa/reset.css">
<link rel="stylesheet" type="text/css" href="/isa/style.css">
<link rel="stylesheet" type="text/css" href="/isa/common.css">
<link rel="stylesheet" type="text/css" href="/isa/layout_new.css">
<link rel="stylesheet" type="text/css" href="/isa/top_new.css">
<link rel="stylesheet" type="text/css" href="/isa/print.css" media="print">
<link rel="stylesheet" type="text/css" href="/isa/aly.css">
<script src="/isa/html5shiv.js"></script>
<script src="https://api.welltool.site/js/hptoz.js?lang=ja"></script>
</head>

<body>
<div id="wrapper">

<noscript>
<p>このページではJavaScriptを使用しています。</p>
</noscript>

<div class="mf_finder_container">
<header class="headerArea">
	<div class="inner">
		<div class="headerLogo"><a href="/isa/index.html"><img src="/isa/content/910000042.png" alt="出入国在留管理庁 Immigration Services Agency of Japan"></a></div>
		<div class="headerGroup">
			<div class="headerGroupA">
				<div class="headerSkip"><a href="#contentsArea">本文へ</a></div>
				<div class="headerLanguage"><a href="/isa/other_languages.html">Multi language</a></div>
			</div>
			<div class="headerGroupB">
				<div class="headerSearch">
					<div class="mars_wrap">

<!-- ↓検索窓　カスタムエレメント -->
<mf-search-box ajax-url="//mf2ap004.marsflag.com/moj__isa__ja/x_search.x" doctype-csv="html,pdf,doc,xls,ppt" doctype-hidden="" imgsize-default="1" options-hidden="" serp-url="/isa/search_result.html#/" submit-text="検索" placeholder="キーワードを入力してください。" :suggest-max="0" ></mf-search-box>
</div>
				</div>
				<div class="headerFontsize">
					<dl>
						<dt>文字サイズ</dt>
						<dd class="standard"><a href="javascript:textSizeReset();">標準</a></dd>
						<dd class="large"><a href="javascript:textSizeUp();">拡大</a></dd>
					</dl>
				</div>
			</div>
		</div>
		<div class="headerBtn"><span></span><span></span><span></span><em>MENU</em></div>
	</div>
	<div class="headerIllust"><img src="/isa/content/001345705.png" alt=""></div>
</header>

<div class="headerNav">
	<div class="inner" role="navigation">
		<ul class="headerNavList">
			<li class="headerNavItem navItem01"><a href="/isa/about/index.html">組織・採用</a>
				<div class="subNav">
					<div class="inner">
						<ul>

			<li><a href="/isa/about/organization/index.html">出入国在留管理庁の概要</a></li>

			<li><a href="/isa/about/organization/chart.html">機構図</a></li>

			<li><a href="/isa/about/organization/executive.html">幹部一覧</a></li>

			<li><a href="/isa/about/region/index.html">地方出入国在留管理官署</a></li>

			<li><a href="/isa/about/recruitment/index.html">採用案内</a></li>
						</ul>
					</div>
				</div>
			</li>
			<li class="headerNavItem navItem02"><a href="/isa/immigration/index.html">出入国手続</a>
				<div class="subNav">
					<div class="inner">
						<ul>

			<li><a href="/isa/immigration/procedures/tetuduki_index1_1.html">出入（帰）国手続について（解説）</a></li>

			<li><a href="/isa/immigration/procedures/16-5.html">再入国許可申請</a></li>

			<li><a href="/isa/immigration/procedures/ttp2_index.html">トラスティド・トラベラー・プログラム（TTP）について</a></li>

			<li><a href="/isa/immigration/procedures/06_00005.html">乗員上陸許可申請（NACCS）</a></li>

			<li><a href="/isa/immigration/resources/index.html">各種公表資料</a></li>

			<li><a href="/isa/immigration/faq/kanri_qa.html">出入国審査・在留審査Q&A</a></li>
						</ul>
					</div>
				</div>
			</li>
			<li class="headerNavItem navItem03"><a href="/isa/applications/index.html">在留手続</a>
				<div class="subNav">
					<div class="inner">
						<ul>

			<li><a href="/isa/applications/status/index.html">在留資格から探す</a></li>

			<li><a href="/isa/applications/procedures/index.html">手続の種類から探す</a></li>

			<li><a href="/isa/applications/online/onlineprocedures.html">オンライン手続</a></li>

			<li><a href="/isa/applications/index_00005.html">育成就労制度</a></li>

			<li><a href="/isa/applications/ssw/index.html">特定技能制度</a></li>

			<li><a href="/isa/applications/titp/nyuukokukanri05_00014.html">外国人技能実習制度について</a></li>

			<li><a href="/isa/applications/resources/newimmiact_3_index.html">高度人材ポイント制</a></li>

			<li><a href="/isa/applications/resources/nyukan_nyukan50_00002.html">J-Skip/J-Find</a></li>

			<li><a href="/isa/applications/resources/03_00070.html">スタートアップ関連施策</a></li>

			<li><a href="/isa/applications/resources/index.html">各種公表資料</a></li>
						</ul>
					</div>
				</div>
			</li>
			<li class="headerNavItem navItem04"><a href="/isa/support/index.html">在留支援</a>
				<div class="subNav">
					<div class="inner">
						<ul>

			<li><a href="/isa/support/coexistence/04_00033.html">ロードマップ</a></li>

			<li><a href="/isa/support/coexistence/nyuukokukanri01_00140.html">総合的対応策</a></li>


			<li><a href="/isa/support/coexistence/04_00070.html">HarmoniUP!</a></li>

			<li><a href="/isa/support/guidance/index.html">新規入国者向けガイダンスページ</a></li>

			<li><a href="/isa/support/portal/index.html">外国人生活支援ポータルサイト</a></li>

			<li><a href="/isa/support/portal/guidebook_all.html">生活・就労ガイドブック</a></li>


			<li><a href="/isa/support/fresc/fresc01.html">外国人在留支援センター(FRESC／フレスク）</a></li>



			<li><a href="/isa/support/consultation/index.html">相談窓口</a></li>






			<li><a href="/isa/support/opinion.html">外国人との共生施策に係る御意見・御要望（御意見箱）</a></li>
						</ul>
					</div>
				</div>
			</li>
			<li class="headerNavItem navItem05"><a href="/isa/deportation/index.html">退去強制手続</a>
				<div class="subNav">
					<div class="inner">
						<ul>

			<li><a href="/isa/deportation/procedures/tetuduki_index5_00002.html">退去強制手続と出国命令制度</a></li>

			<li><a href="/isa/08_00045.html">監理措置制度</a></li>

			<li><a href="/isa/08_00050.html">仮放免制度</a></li>

			<li><a href="/isa/deportation/application.html">各種申請</a></li>

			<li><a href="/isa/consultation/report/index.html">入管法違反者に関する情報提供</a></li>

			<li><a href="/isa/deportation/resources/index.html">各種公表資料</a></li>

			<li><a href="/isa/deportation/guide/tetuduki_taikyo_qa.html">退去強制手続・出国命令制度・上陸拒否期間の短縮決定Q&A</a></li>
						</ul>
					</div>
				</div>
			</li>
			<li class="headerNavItem navItem06"><a href="/isa/refugee/index.html">難民の認定等</a>
				<div class="subNav">
					<div class="inner">
						<ul>

			<li><a href="/isa/refugee/procedures/16-6.html">難民認定手続・補完的保護対象者認定手続</a></li>

			<li><a href="/isa/refugee/procedures/16-7.html">難民旅行証明書交付申請手続</a></li>

			<li><a href="/isa/refugee/procedures/07_00046.html">第三国定住による難民の受入れ</a></li>

			<li><a href="/isa/refugee/procedures/higo_00001.html">一時庇護上陸許可</a></li>

			<li><a href="/isa/refugee/resources/index.html">各種公表資料</a></li>
						</ul>
					</div>
				</div>
			</li>
			<li class="headerNavItem navItem07"><a href="/isa/policies/index.html">政策情報（会議・統計等）</a>
				<div class="subNav">
					<div class="inner">
						<ul>

			<li><a href="/isa/policies/conference/seisaku_index.html">会議・委員会等</a></li>

			<li><a href="/isa/policies/statistics/index.html">統計</a></li>

			<li><a href="/isa/policies/policies/index.html">基本計画・白書・パンフレット</a></li>

			<li><a href="/isa/policies/bill/01_00442.html">関係法令</a></li>

			<li><a href="/isa/policies/others/index.html">その他政策情報</a></li>
						</ul>
					</div>
				</div>
			</li>
			<li class="headerNavItem navItem08"><a href="/isa/publications/index.html">広報・情報公開等</a>
				<div class="subNav">
					<div class="inner">
						<ul>

			<li><a href="/isa/publications/press/index.html">プレスリリース</a></li>

			<li><a href="/isa/publications/newslist/index.html">更新情報</a></li>

			<li><a href="/isa/publications/publications/index.html">広報</a></li>

			<li><a href="/isa/publications/procurement/index.html">調達情報</a></li>

			<li><a href="/isa/publications/disclosure/index.html">情報公開</a></li>

			<li><a href="/isa/publications/privacy/index.html">個人情報保護</a></li>

			<li><a href="/isa/publications/record/index.html">公文書管理</a></li>

			<li><a href="/isa/publications/contact/index.html">ご意見・情報提供</a></li>

			<li><a href="/isa/publications/others/index.html">その他の公表情報</a></li>

			<li><a href="/isa/publications/faq.html">よくあるお問い合わせ（FAQ）</a></li>
						</ul>
					</div>
				</div>
			</li>
		</ul>
	</div>
</div>

<div class="spNav">
	<div class="inner">
		<ul class="spNavList">
			<li class="spNavItem navItem01"><a href="/isa/about/index.html">組織・採用</a>
						<ul>

			<li><a href="/isa/about/organization/index.html">出入国在留管理庁の概要</a></li>

			<li><a href="/isa/about/organization/chart.html">機構図</a></li>

			<li><a href="/isa/about/organization/executive.html">幹部一覧</a></li>

			<li><a href="/isa/about/region/index.html">地方出入国在留管理官署</a></li>

			<li><a href="/isa/about/recruitment/index.html">採用案内</a></li>
						</ul>
			</li>
			<li class="spNavItem navItem02"><a href="/isa/immigration/index.html">出入国手続</a>
						<ul>

			<li><a href="/isa/immigration/procedures/tetuduki_index1_1.html">出入（帰）国手続について（解説）</a></li>

			<li><a href="/isa/immigration/procedures/16-5.html">再入国許可申請</a></li>

			<li><a href="/isa/immigration/procedures/ttp2_index.html">トラスティド・トラベラー・プログラム（TTP）について</a></li>

			<li><a href="/isa/immigration/procedures/06_00005.html">乗員上陸許可申請（NACCS）</a></li>

			<li><a href="/isa/immigration/resources/index.html">各種公表資料</a></li>

			<li><a href="/isa/immigration/faq/kanri_qa.html">出入国審査・在留審査Q&A</a></li>
						</ul>
			</li>
			<li class="spNavItem navItem03"><a href="/isa/applications/index.html">在留手続</a>
						<ul>

			<li><a href="/isa/applications/status/index.html">在留資格から探す</a></li>

			<li><a href="/isa/applications/procedures/index.html">手続の種類から探す</a></li>

			<li><a href="/isa/applications/online/onlineprocedures.html">オンライン手続</a></li>

			<li><a href="/isa/applications/index_00005.html">育成就労制度</a></li>

			<li><a href="/isa/applications/ssw/index.html">特定技能制度</a></li>

			<li><a href="/isa/applications/titp/nyuukokukanri05_00014.html">外国人技能実習制度について</a></li>

			<li><a href="/isa/applications/resources/newimmiact_3_index.html">高度人材ポイント制</a></li>

			<li><a href="/isa/applications/resources/nyukan_nyukan50_00002.html">J-Skip/J-Find</a></li>

			<li><a href="/isa/applications/resources/03_00070.html">スタートアップ関連施策</a></li>

			<li><a href="/isa/applications/resources/index.html">各種公表資料</a></li>
						</ul>
			</li>
			<li class="spNavItem navItem04"><a href="/isa/support/index.html">在留支援</a>
						<ul>

			<li><a href="/isa/support/coexistence/04_00033.html">ロードマップ</a></li>

			<li><a href="/isa/support/coexistence/nyuukokukanri01_00140.html">総合的対応策</a></li>


			<li><a href="/isa/support/coexistence/04_00070.html">HarmoniUP!</a></li>

			<li><a href="/isa/support/guidance/index.html">新規入国者向けガイダンスページ</a></li>

			<li><a href="/isa/support/portal/index.html">外国人生活支援ポータルサイト</a></li>

			<li><a href="/isa/support/portal/guidebook_all.html">生活・就労ガイドブック</a></li>


			<li><a href="/isa/support/fresc/fresc01.html">外国人在留支援センター(FRESC／フレスク）</a></li>



			<li><a href="/isa/support/consultation/index.html">相談窓口</a></li>






			<li><a href="/isa/support/opinion.html">外国人との共生施策に係る御意見・御要望（御意見箱）</a></li>
						</ul>
			</li>
			<li class="spNavItem navItem05"><a href="/isa/deportation/index.html">退去強制手続</a>
						<ul>

			<li><a href="/isa/deportation/procedures/tetuduki_index5_00002.html">退去強制手続と出国命令制度</a></li>

			<li><a href="/isa/08_00045.html">監理措置制度</a></li>

			<li><a href="/isa/08_00050.html">仮放免制度</a></li>

			<li><a href="/isa/deportation/application.html">各種申請</a></li>

			<li><a href="/isa/consultation/report/index.html">入管法違反者に関する情報提供</a></li>

			<li><a href="/isa/deportation/resources/index.html">各種公表資料</a></li>

			<li><a href="/isa/deportation/guide/tetuduki_taikyo_qa.html">退去強制手続・出国命令制度・上陸拒否期間の短縮決定Q&A</a></li>
						</ul>
			</li>
			<li class="spNavItem navItem06"><a href="/isa/refugee/index.html">難民の認定等</a>
						<ul>

			<li><a href="/isa/refugee/procedures/16-6.html">難民認定手続・補完的保護対象者認定手続</a></li>

			<li><a href="/isa/refugee/procedures/16-7.html">難民旅行証明書交付申請手続</a></li>

			<li><a href="/isa/refugee/procedures/07_00046.html">第三国定住による難民の受入れ</a></li>

			<li><a href="/isa/refugee/procedures/higo_00001.html">一時庇護上陸許可</a></li>

			<li><a href="/isa/refugee/resources/index.html">各種公表資料</a></li>
						</ul>
			</li>
			<li class="spNavItem navItem07"><a href="/isa/policies/index.html">政策情報（会議・統計等）</a>
						<ul>

			<li><a href="/isa/policies/conference/seisaku_index.html">会議・委員会等</a></li>

			<li><a href="/isa/policies/statistics/index.html">統計</a></li>

			<li><a href="/isa/policies/policies/index.html">基本計画・白書・パンフレット</a></li>

			<li><a href="/isa/policies/bill/01_00442.html">関係法令</a></li>

			<li><a href="/isa/policies/others/index.html">その他政策情報</a></li>
						</ul>
			</li>
			<li class="spNavItem navItem08"><a href="/isa/publications/index.html">広報・情報公開等</a>
						<ul>

			<li><a href="/isa/publications/press/index.html">プレスリリース</a></li>

			<li><a href="/isa/publications/newslist/index.html">更新情報</a></li>

			<li><a href="/isa/publications/publications/index.html">広報</a></li>

			<li><a href="/isa/publications/procurement/index.html">調達情報</a></li>

			<li><a href="/isa/publications/disclosure/index.html">情報公開</a></li>

			<li><a href="/isa/publications/privacy/index.html">個人情報保護</a></li>

			<li><a href="/isa/publications/record/index.html">公文書管理</a></li>

			<li><a href="/isa/publications/contact/index.html">ご意見・情報提供</a></li>

			<li><a href="/isa/publications/others/index.html">その他の公表情報</a></li>

			<li><a href="/isa/publications/faq.html">よくあるお問い合わせ（FAQ）</a></li>
						</ul>
			</li>
		</ul>
		<div class="spNavGroup">
			<div class="spNavLanguage"><a href="/isa/other_languages.html">Multi language</a></div>
			<div class="spNavSearch">
				<div class="mars_wrap">

<!-- ↓検索窓　カスタムエレメント -->
<mf-search-box ajax-url="//mf2ap004.marsflag.com/moj__isa__ja/x_search.x" doctype-csv="html,pdf,doc,xls,ppt" doctype-hidden="" imgsize-default="1" options-hidden="" serp-url="/isa/search_result.html#/" submit-text="検索" placeholder="キーワードを入力してください。" :suggest-max="0"></mf-search-box>

</div>
			</div>
		</div>
	</div>
</div>
</div>

<div class="topSlide">
	<div class="inner">
		<ul class="slides">
			<li>
				<div class="card"><a href="/isa/support/coexistence/04_00070.html"><img src="/isa/content/001442083.png" alt="HarmoniUP!"></a>
</div>
			</li>
			<li>
				<div class="card"><a href="/isa/support/fresc/fresc01.html"><img src="/isa/content/001425729.png" alt="外国人在留支援センター"></a>
</div>
			</li>
			<li>
				<div class="card"><a href="/isa/support/portal/index.html"><img src="/isa/content/001427563.png" alt="外国人生活支援ポータルサイト"></a>
</div>
			</li>
			<li>
				<div class="card"><a href="/isa/applications/ssw/index.html"><img src="/isa/content/001329903.png" alt="特定技能制度について"></a>
</div>
			</li>
			<li>
				<div class="card"><a href="/isa/applications/online/onlineshinsei.html"><img src="/isa/content/001362137.png" alt="在留申請手続はオンライン申請が便利です。"></a>
</div>
			</li>
			<li>
				<div class="card"><a href="/isa/applications/procedures/rcc-support.html"><img src="/isa/content/001337279.jpg" alt="在留カード等読取アプリケーション"></a>
</div>
			</li>
			<li>
				<div class="card"><a href="/isa/applications/resources/nyukan_nyukan50_00002.html"><img src="/isa/content/001395291.png" alt="特別高度人材制度・未来創造人材制度がはじまりました。"></a>
</div>
			</li>
			<li>
				<div class="card"><a href="/isa/10_00219.html"><img src="/isa/content/001440032.jpg" alt="入国前結核スクリーニング"></a>
</div>
			</li>
		</ul>
		<div class="slidesControl">
			<ul>
				<li class="slidesPlay" tabindex="0"><p>再生ボタン</p></li>
				<li class="slidesStop" tabindex="0"><p>停止ボタン</p></li>
			</ul>
		</div>
	</div>
</div>


<div id="contentsArea" class="contentsArea topContentsArea">


	<div class="topSection">
		<div class="inner">
<style type="text/css">/* コンテナの設定 */
.button-container {
  display: flex;
  justify-content: space-evenly;
  gap: 20px; 
  flex-wrap: nowrap;
  width: 100%; 
  padding-top:20px;
  padding-bottom:20px;
}

/* ボタンアイテムの設定 */
.button-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start; 
  text-align: center;
  max-width: 200px; 
}

.button-item a{
  color:#000000;
}

/* ボタンの設定 */
.button {
  display: flex;
  flex-direction: column; 
  align-items: center; 
  justify-content: center; 
  padding: 10px;
  text-decoration: none;
  border-radius: 5px; 
  color: #000; 
  background-color: #deebf7;
  transition: background-color 0.3s; 
  height: 100%;
  position: relative; 
  width: 100%; 
  box-sizing: border-box;
  border: 3px solid #65abde;
 font-size: 15px;
}

/* アイコンの固定位置 */
.button img {
  width: 45%;
  height: auto;
  margin: 20px 0 20px 0;
  position: relative; 
}

/* 色設定 */
/*
.color1 {
  background-color: #FF5733; 
}

.color2 {
  background-color: #33B5FF; 
}

.color3 {
  background-color: #FFEB3B; 
}

.color4 {
  background-color: #9C27B0; 
}

.color5 {
  background-color: #FF9800; 
}

.color6 {
  background-color: #FF9800; 
}

*/
/* ボタンにホバー時の色変化を追加 */
.button:hover {
  background-color: #c5d2ed; /* ホバー時の背景色を変更 */
}

/* ボタンアイテムの高さ調整 */
.button-item {
  min-height: 120px; /* ボタンの高さを確保 */
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* 高さを均等に分ける */
}

/* レスポンシブデザイン - スマートフォン表示時 */
@media (max-width: 768px) {
  .button-container {
    display: grid; /* Gridレイアウトに変更 */
    grid-template-columns: repeat(3, 1fr); /* 1行3個に表示 */
    gap: 10px; /* アイテム間の隙間を調整 */
  }

  .button-item {
    min-width: 0; /* 最小幅を設定せず、ボタン幅を調整 */
  }

  .button {
    width: 100%; /* ボタンの幅を100%にして横並びを維持 */
     font-size: 12px; /* フォントサイズを小さく */
    padding: 15px 5px; /* 高さが揃うように調整 */
  }

  /* アイコンとテキストの位置調整 */
  .button img {
    margin-bottom: 10px; /* アイコンとテキストの間隔調整 */
  }
}

/* PC表示時にボタン間の隙間を調整 */
@media (min-width: 769px) {
  .button-container {
    gap: 20px; /* PC表示時に十分なボタン間の隙間を確保 */
  }

  .button-item {
    max-width: 200px; /* PCでボタンが重ならないように最大幅を設定 */
  }
  #contentsArea > div:nth-child(1) > div > ul.dateList02{
    padding-bottom:20px;
  }
}
</style>
<div class="button-container">
	<div class="button-item"><a class="button color1" href="https://www.moj.go.jp/isa/about/organization/naruhodonyuukan.html"><img alt="出入国在留管理庁のアイコン" src="/isa/content/001435222.png"> なるほど！<br>
		出入国在留管理庁 </a></div>

	<div class="button-item"><a class="button color2" href="/isa/chumokuseisaku.html"><img alt="注目政策情報のアイコン" src="/isa/content/001435577.png"> 注目政策情報 </a></div>

	<div class="button-item"><a class="button color3" href="/isa/publications/faq.html"><img alt="よくあるお問合わせのアイコン" src="/isa/content/001435224.png"> よくあるお問い合わせ<br>
		(FAQ) </a></div>

	<div class="button-item"><a class="button color4" href="/isa/support/consultation/index.html"><img alt="相談窓口のアイコン" src="/isa/content/001435225.png"> 相談窓口 </a></div>

	<div class="button-item"><a class="button color5" href="/isa/support/portal/index.html"><img alt="外国人生活支援のアイコン" src="/isa/content/001435584.png"> 外国人生活支援<br>
		ポータルサイト </a></div>

	<div class="button-item"><a class="button color6" href="/isa/01_00469.html"><img alt="災害時に役立つ情報のアイコン" src="/isa/content/001435579.png"> 災害時に役立つ情報 </a></div>
</div>

<h2 class="titleStyle01">大事なお知らせ</h2>

<ul class="dateList02">
	<li>
		<div class="txt"><a href="/isa/10_00219.html">入国前結核スクリーニングの開始予定について</a></div>
	</li>
	<li>
		<div class="txt"><a href="/isa/10_00229.html">ミャンマー国籍の方の在留資格認定証明書の有効期間の延長について</a></div>
	</li>
	<li>
		<div class="txt"><a href="/isa/expo2025.html">大阪・関西万博について</a></div>
	</li>
	<li>
		<div class="txt"><a href="/isa/01_00518.html">在留手続等に関する手数料の改定について</a></div>
	</li>
	<li>
		<div class="txt"><a href="/isa/01_00492.html">出入国在留管理庁代表電話番号の変更について（変更日 : 令和７年１月９日）</a></div>
	</li>
	<li>
		<div class="txt"><a href="/isa/01_00461.html">令和６年入管法等改正法について</a></div>
	</li>
	<li>
		<div class="txt"><a href="/isa/publications/others/nyuukokukanri01_00142.html">入管を名乗る不審な電話、メール等にご注意ください</a></div>
	</li>
</ul>

		</div>
	</div>


	<div class="topSection topSectionBg01 topSectionClm">
		<div class="inner">

			<div class="topMain">
				<div class="topNewsMenu">
					<div class="ttl">更新情報</div>
					<ul class="list">
						<li><a href="/isa/publications/newslist/list-procedures.html" class="cat03">制度・手続案内</a></li>
						<li><a href="/isa/publications/newslist/list-support.html" class="cat05">在留支援</a></li>
						<li><a href="/isa/publications/newslist/list-press.html" class="cat04">報道・公表資料</a></li>
						<li><a href="/isa/publications/newslist/list-supply.html" class="cat01">調達・採用情報</a></li>
						<li><a href="/isa/publications/newslist/other.html" class="cat02">その他</a></li>
					</ul>
				</div>
		<ul class="dateList02">
			<li>
				<div class="date">2025年7月28日</div>
				<div class="cat"><span class="cat03">制度・手続案内</span></div>
				<div class="txt">
					<a href="/isa/applications/ssw/nyuukokukanri06_00103.html">特定技能ガイドブック～特定技能外国人の雇用を考えている事業者の方へ～を更新しました。</a>
				</div>
			</li>
			<li>
				<div class="date">2025年7月28日</div>
				<div class="cat"><span class="cat03">制度・手続案内</span></div>
				<div class="txt">
					<a href="/isa/applications/ssw/nyuukokukanri06_00064.html">特定技能ガイドブック～特定技能の在留資格で働くことを考えている外国人の方へ～を更新しました。</a>
				</div>
			</li>
			<li>
				<div class="date">2025年7月28日</div>
				<div class="cat"><span class="cat01">調達・採用情報</span></div>
				<div class="txt">
					<a href="/isa/about/region/sapporo/recruit.html">【札幌出入国在留管理局】国家公務員オープンゼミのお知らせを掲載しました。</a>
				</div>
			</li>
			<li>
				<div class="date">2025年7月22日</div>
				<div class="cat"><span class="cat01">調達・採用情報</span></div>
				<div class="txt">
					<a href="/isa/about/region/naha/recruit.html">【福岡出入国在留管理局那覇支局】非常勤職員（期間業務職員）の募集案内を掲載いたしました。</a>
				</div>
			</li>
			<li>
				<div class="date">2025年7月22日</div>
				<div class="cat"><span class="cat01">調達・採用情報</span></div>
				<div class="txt">
					<a href="/isa/about/region/osaka/recruit.html">【大阪出入国在留管理局】第２回官庁訪問のご案内について</a>
				</div>
			</li>
		</ul>

				<div class="topNewsBtn"><a href="/isa/publications/newslist/index.html">更新情報一覧へ</a></div>
			</div>

			<div class="topAside">
<ul>
	<li><span style="background-color:#d9eff9;">【<a href="/isa/10_00210.html">在留諸申請の進捗確認について </a>】</span></li>
</ul>
<br><br>
<a href="/isa/applications/online/onlineprocedures.html"><img alt="オンライン申請" src="/isa/content/001435424.png"></a>
<ul>
	<li>　　<a href="/isa/applications/online/onlineprocedures.html">オンライン申請 </a></li>
</ul>
<a href="/isa/publications/privacy/index.html"><img alt="開示請求手続" src="/isa/content/001435425.png"></a>

<ul>
	<li>　　<a href="/isa/publications/privacy/index.html">開示請求手続</a></li>
</ul>
<a href="/isa/about/recruitment/index.html"><img alt="採用情報" src="/isa/content/001435426.png"></a>

<ul>
	<li>　　<a href="/isa/about/recruitment/index.html">採用情報</a></li>
</ul>
<a href="/isa/publications/publications/index.html"><img alt="広報活動" src="/isa/content/001435427.png"></a>

<ul>
	<li>　　<a href="/isa/publications/publications/index.html">広報活動</a></li>
</ul>
<br>
<script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>

			</div>

		</div>
	</div>



	<div class="topSection">
		<div class="inner">
			<h2 class="titleStyle01">カテゴリから探す</h2>
<style type="text/css">.banner-top{
display: flex;
flex-wrap:wrap;
}
.banner-top li {
width: calc(100%/3);
padding:10px 10px;
box-sizing:border-box;
}
.banner-top li img {
max-width:100%;
height: auto;
border:solid 0px;
}
.keyword{
padding:10px;
border-radius:10px;
background-color:#e0ffff;
font-family:"BIZ UDゴシック",sans-serif;
}


    /* コンテナ（横並び） */
    .display-container {
      display: flex;
      justify-content: space-between;
      gap: 20px;
      padding: 0 20px;
      margin-bottom: 20px; 
      width: 100%;
      box-sizing: border-box;
    }

    /* 項目 */
    .display-item {
      border-radius: 5px;
      margin: 5px 0;
      flex-grow: 1;
      box-sizing: border-box;
      position: relative;
      width: calc(100% / 5);
    }

    /* ボタン部分 */
    .display-header {
  background-color: #007BFF;
  color: white;
  min-height:120px;
  padding: 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease;
  width: 100%;
  outline: none;
  position: relative;
  display: flex;
  align-items: center; 
  justify-content: center;
  flex-direction: column;
  text-decoration: none;
  box-sizing: border-box;
  font-size:1.0em;
    }

.display-header img {
    width: 40%;
    height: auto;
    margin: 20px 0 20px 0;
    position: relative;
}

/* ボタンの色（個別設定） */
.display-header.color01 {
  background-color:#6FADD4;
}

.display-header.color02 {
  background-color: #FFC000;
}

.display-header.color03 {
  background-color: #69AE64;
}

    .display-header.color04 {
      background-color: #4A66B3;
    }

    .display-header.color05 {
      background-color: #F4B183;
    }

/* ボタンが開いているときの色 */
.display-header.open.color01 {
  background-color: #6FADD4;
}

.display-header.open.color02 {
  background-color: #FFC000;
}

.display-header.open.color03 {
  background-color: #69AE64;
}

    .display-header.open.color04 {
      background-color: #4A66B3;
    }

    .display-header.open.color05 {
      background-color: #F4B183;
    }

/* 吹き出しの三角形の色も変更 */
.display-header.color01::after {
  border-top: 10px solid #6FADD4;
}

.display-header.color02::after {
  border-top: 10px solid #FFC000;
}

.display-header.color03::after {
  border-top: 10px solid #69AE64;
}

    .display-header.color04::after {
      border-top: 10px solid #4A66B3;
    }

    .display-header.color05::after {
      border-top: 10px solid #F4B183;
    }

/* アコーディオンの背景色（個別設定） */
.display-area.color01 {
  background-color: #DEEBF7;
}

.display-area.color02 {
  background-color: #FFE699;
}

.display-area.color03 {
  background-color: #A9D18E;
}
    .display-area.color04 {
      background-color: #8FAADC;
    }

    .display-area.color05 {
      background-color: #FBE5D6;
    }


    /* 開いているボタンのスタイル */
    .display-header.open {
      background-color: #0056b3;
      transform: scale(1.05);
    }

    /* 吹き出し（三角形）の初期設定 */
    .display-header::after {
      content: "";
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-top: 10px solid #007BFF;
      display: none;
      transition: all 0.3s ease;
    }

    /* display-headerが開いたときに三角形を表示 */
    .display-header.open::after {
      display: block;
    }

    /* displayコンテンツ（最初は非表示） */
    .display-content {
      display: none;
    }

    /* display-areaの初期設定 */
    .display-area {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.5s ease-out, padding 0.5s ease;
      background-color: #f9f9f9;
      padding: 0 20px;
      margin-top: 10px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      width: 100%;
      box-sizing: border-box;
      display: none;
      z-index:1;
    }

    /* display-areaが開いたときの設定 */
    .display-area.open {
      padding: 15px;
      display: block;
      max-height: 5000px;
      margin-top: 10px;
      margin-bottom: 20px; 
    }

    .display-area span{
      display:block;
      padding:10px 0;

    }

    .display-area ul {
      display: flex;
      flex-wrap: wrap;
      gap: 10px 20px;
      margin-bottom:20px;
    }

    .display-area ul li {
      display: grid;
      gap: 10px 20px;
    }


@media (max-width: 768px) {
  /* 親コンテナ（ボタンがグリッドレイアウトに並ぶ） */
  .display-container {
    display: grid;
    grid-template-columns: repeat(6, 1fr); /* 1行に6つのカラムを設定 */
    grid-template-rows: repeat(2, 1fr);    /* 1列に2つの行を設定 */
    grid-column-gap: 10px;
    grid-row-gap: 10px;
    padding: 0;
    margin: 0;
  }

  /* 各アイテムの配置 */
  .display-item:nth-child(1) {
    grid-area: 1 / 1 / 2 / 3;  /* 1行目の1カラムから3カラムにまたがる */
  }
  .display-item:nth-child(2) {
    grid-area: 1 / 3 / 2 / 5;  /* 1行目の3カラムから5カラムにまたがる */
  }
  .display-item:nth-child(3) {
    grid-area: 1 / 5 / 2 / 7;  /* 1行目の5カラムから7カラムにまたがる */
  }
  .display-item:nth-child(4) {
    grid-area: 2 / 2 / 3 / 4;  /* 2行目の2カラムから4カラムにまたがる */
  }
  .display-item:nth-child(5) {
    grid-area: 2 / 4 / 3 / 6;  /* 2行目の4カラムから6カラムにまたがる */
  }

  /* アイテムの見た目調整 */
  .display-item {
    border-radius: 5px;
    box-sizing: border-box;
    position: relative;
    width:100%;
  }

  .display-header {
    background-color: #007BFF;
    color: white;
    min-height: 120px;
    padding: 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
    width: 100%;
    height: 100%;
    outline: none;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-decoration: none;
  }

  .display-header img {
    width: 40%;
    height: auto;
    margin: 20px 0;
  }
}
ul.cp_list {
  padding:0.5em;
  list-style-type: none;
}
ul.cp_list li {
  position: relative;
  padding: 0em 0.3em 0.8em 1.3em;
  display: flex;
  align-items: center;
}
ul.cp_list li::before,
ul.cp_list li::after {
  position: absolute;
  content: '';
}
ul.cp_list li::before {
  top: 0.2em;
  left: 0em;
  width: 15px;
  height: 15px;
  background-color: #17184b;
  border-radius: 100%;
}
ul.cp_list li::after {
  top: 0.45em;
  left: 0.25em;
  width: 4px;
  height: 4px;
  border-right: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(-45deg);
}
</style>
<div class="display-container">
	<div class="display-item"><button aria-expanded="false" class="display-header color01"><img alt="出入国管理のアイコン" class="button-icon" src="/isa/content/001435228.png"> 出入国手続</button>
		<div class="display-content"><span><a href="/isa/immigration/index.html"><font size="4.5em"><b>出入国手続</b></font></a></span>
			<ul class="cp_list ">
				<li><a href="/isa/immigration/procedures/tetuduki_index1_1.html">出入（帰）国手続について（解説）</a></li>
				<li><a href="/isa/immigration/procedures/16-5.html">再入国許可申請</a></li>
				<li><a href="/isa/immigration/procedures/ttp2_index.html">トラスティド・トラベラー・プログラム（TTP）について</a></li>
				<li><a href="/isa/immigration/procedures/06_00005.html">乗員上陸許可申請（NACCS）</a></li>
				<li><a href="/isa/immigration/resources/index.html">各種公表資料</a></li>
				<li><a href="/isa/immigration/faq/kanri_qa.html">出入国審査・在留審査Q＆A</a></li>
			</ul>
		</div>
	</div>

	<div class="display-item"><button aria-expanded="false" class="display-header color02"><img alt="在留手続のアイコン" class="button-icon" src="/isa/content/001435581.png"> 在留手続</button>

		<div class="display-content"><span><a href="/isa/applications/index.html"><font size="4.5em"><b>在留手続</b></font></a></span>

			<ul class="cp_list ">
				<li><a href="/isa/applications/procedures/index.html">手続の種類から探す</a></li>
				<li><a href="/isa/applications/status/index.html">在留資格から探す</a></li>
				<li><a href="/isa/applications/online/onlineprocedures.html">オンライン申請</a></li>
				<li><a href="/isa/applications/index_00005.html">育成就労制度について</a></li>
				<li><a href="/isa/applications/ssw/index.html">特定技能制度について</a></li>
				<li><a href="/isa/applications/titp/nyuukokukanri05_00014.html">技能実習制度について</a></li>
				<li><a href="/isa/applications/resources/newimmiact_3_index.html">高度人材ポイント制について</a></li>
				<li><a href="/isa/applications/resources/nyuukokukanri01_00009.html">特別高度人材制度（J-Skip）について</a></li>
				<li><a href="/isa/applications/status/designatedactivities51.html">未来創造人材制度（J-Find）について</a></li>
				<li><a href="/isa/applications/resources/index.html">各種公表資料</a></li>
			</ul>
		</div>
	</div>

	<div class="display-item"><button aria-expanded="false" class="display-header color03"><img alt="在留支援のアイコン" class="button-icon" src="/isa/content/001435230.png"> 在留支援</button>

		<div class="display-content"><span><a href="/isa/support/index.html"><font size="4.5em"><b>在留支援</b></font></a></span>

			<ul class="cp_list ">
				<li><a href="/isa/support/coexistence/04_00033.html">外国人との共生社会の実現に向けたロードマップ</a></li>
				<li><a href="/isa/support/coexistence/nyuukokukanri01_00140.html">外国人材の受入れ・共生のための総合的対応策</a></li>
				<li><a href="/isa/support/coexistence/04_00070.html">HarmoniUP！（ハーモニアップ！）</a></li>
				<li><a href="/isa/support/guidance/index.html">新規入国者向けガイダンスページ</a></li>
				<li><a href="/isa/support/portal/index.html">外国人生活支援ポータルサイト</a></li>
				<li><a href="/isa/support/portal/guidebook_all.html">生活・就労ガイドブック</a></li>
				<li><a href="/isa/support/fresc/fresc01.html">外国人在留支援センター（FRESC）</a></li>
				<li><a href="/isa/support/consultation/index.html">相談窓口</a></li>
				<li><a href="/isa/support/opinion.html">外国人との共生施策に係る御意見・御要望（御意見箱）</a></li>
			</ul>
		</div>
	</div>

	<div class="display-item"><button aria-expanded="false" class="display-header color04"><img alt="退去強制手続のアイコン" class="button-icon" src="/isa/content/001435231.png"> 退去強制手続</button>

		<div class="display-content"><span><a href="/isa/deportation/index.html"><font size="4.5em"><b>退去強制手続</b></font></a></span>

			<ul class="cp_list ">
				<li><a href="/isa/deportation/procedures/tetuduki_index5_00002.html">退去強制手続と出国命令制度</a></li>
				<li><a href="/isa/08_00045.html">監理措置制度</a></li>
				<li><a href="/isa/08_00050.html">仮放免制度</a></li>
				<li><a href="/isa/deportation/application.html">各種申請</a></li>
				<li><a href="/isa/deportation/resources/index.html">各種公表資料</a></li>
				<li><a href="/isa/deportation/guide/tetuduki_taikyo_qa.html">退去強制手続・出国命令制度・上陸拒否期間の短縮決定Q＆A</a></li>
			</ul>
		</div>
	</div>

	<div class="display-item"><button aria-expanded="false" class="display-header color05"><img alt="難民の認定等のアイコン" class="button-icon" src="/isa/content/001435232.png"> 難民の認定等</button>

		<div class="display-content"><span><a href="/isa/refugee/index.html"><font size="4.5em"><b>難民の認定等</b></font></a></span>

			<ul class="cp_list ">
				<li><a href="/isa/refugee/procedures/16-6.html">難民認定制度・補完的保護対象者認定手続</a></li>
				<li><a href="[[[pageid=9300010207]]]">難民旅行証明書交付申請手続</a></li>
				<li><a href="/isa/refugee/procedures/07_00046.html">第三国定住による難民の受入れ</a></li>
				<li><a href="/isa/refugee/procedures/higo_00001.html">一時庇護上陸許可</a></li>
				<li><a href="/isa/refugee/resources/index.html">各種公表資料</a></li>
			</ul>
		</div>
	</div>
</div>

<div class="display-area">&nbsp;</div>

<h2 class="titleStyle01">「出入国在留管理庁～その使命と役割～」</h2>

<div class="topVideo">
	<div class="video"><iframe allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen="" frameborder="0" height="315" src="https://www.youtube.com/embed/Ce4yWo1UbOo" title="YouTube video player" width="560"></iframe></div>

	<div class="item">
		<p>皆様は出入国在留管理庁についてご存知ですか？<br>
			出入国在留管理庁は、法務省の外局、つまり、法務省に所属する組織で独自の任務を行っています。<br>
			この動画では、当庁の任務について、皆様に分かりやすくご説明いたします。</p>

		<p>そのほかの動画についてはこちらをご覧ください。<br>
			<a href="/isa/publications/publications/nyuukokukanri01_00182.html">動画ライブラリー</a></p>
	</div>
</div>
<script>
    let isAnimating = false; // アニメーション中かどうかを追跡

document.querySelectorAll('.display-header').forEach(header => {
  header.addEventListener('click', function() {
    if (isAnimating) return;

    isAnimating = true;

    const content = this.nextElementSibling;
    const displayArea = document.querySelector('.display-area');
    const isExpanded = this.getAttribute('aria-expanded') === 'true';

    // 他のボタンの反転を解除
    document.querySelectorAll('.display-header').forEach(otherHeader => {
      if (otherHeader !== this) {
        otherHeader.classList.remove('open');
        otherHeader.setAttribute('aria-expanded', 'false');
      }
    });

    // クリックされたボタンの色に基づいてアコーディオンの背景色を変更
    const colorClass = Array.from(this.classList).find(cls => cls.startsWith('color'));

    // アコーディオンの色を設定
    displayArea.className = 'display-area ' + colorClass;

    if (isExpanded) {
      displayArea.style.maxHeight = '0';
      displayArea.classList.remove('open');
      this.setAttribute('aria-expanded', 'false');
      this.classList.remove('open');

      setTimeout(() => {
        displayArea.style.padding = '0';
        displayArea.innerHTML = '';
        displayArea.style.display = 'none';
        isAnimating = false;
      }, 500);
    } else {
      displayArea.innerHTML = content.innerHTML;

      displayArea.style.display = 'block';
      displayArea.style.padding = '0 20px';
      displayArea.style.maxHeight = '0';

      setTimeout(() => {
        displayArea.style.maxHeight = displayArea.scrollHeight + 'px';
        displayArea.classList.add('open');
        this.setAttribute('aria-expanded', 'true');
        this.classList.add('open');

        setTimeout(() => {
          isAnimating = false;
        }, 500);
      }, 10);
    }
  });

  header.addEventListener('keydown', function(event) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.click();
    }
  });
});


document.addEventListener('DOMContentLoaded', function () {
  const displayArea = document.querySelector('.display-area');
  let isSP = window.innerWidth <= 768; // 初期状態での判定（ウィンドウ幅による判定）
  let previousWidth = window.innerWidth; // 前回のウィンドウ幅を記録

  // SPモードに切り替える処理
  function enterSPMode() {
    const buttons = document.querySelectorAll('.display-header');
    buttons.forEach(button => {
      // SPモード時のみクリックイベントを追加
      button.addEventListener('click', handleButtonClick); 
    });

    // displayAreaのスタイル設定
    if (displayArea) {
      const displayAreaStyle = displayArea.style;
      displayAreaStyle.position = 'absolute'; // positionをabsoluteに設定
    }
  }

  // PCモードに切り替える処理
  function enterPCMode() {
    const buttons = document.querySelectorAll('.display-header');
    buttons.forEach(button => {
      // PCモードではクリックイベントを削除
      button.removeEventListener('click', handleButtonClick); 
    });

    // displayAreaのスタイルを初期化（PC用の状態に戻す）
    if (displayArea) {
      const displayAreaStyle = displayArea.style;
      displayAreaStyle.position = ''; // positionをリセット
      displayAreaStyle.top = ''; // topをリセット

    }
  }

  // ボタンがクリックされた時の処理（SPモードのみ）
  function handleButtonClick(event) {
    const button = event.currentTarget;
    const displayItem = button.closest('.display-item');
    const innerElement = displayItem.closest('.inner'); // 親要素の親、つまりinner要素を取得
    const buttonRect = button.getBoundingClientRect(); // ボタンの位置情報を取得
    const innerRect = innerElement.getBoundingClientRect(); // inner要素の位置情報を取得

    // display-areaのスタイルを設定
    if (displayArea) {
      const displayAreaStyle = displayArea.style;
      displayAreaStyle.position = 'absolute'; // positionをabsoluteに設定

      // inner内でのボタンの位置を基準に、display-areaのtopを設定
      const topPosition = buttonRect.bottom - innerRect.top; // inner内でのボタンの下端位置
      displayAreaStyle.top = `${topPosition}px`; // topを設定
      displayAreaStyle.left = `0px`; // leftを設定

    }
  }

  // SP/PCの切り替えをする関数
  function switchMode(isInitialLoad = false) {
    const currentWidth = window.innerWidth;

    // 初回ロード時にモードを決定
    if (isInitialLoad) {
      if (currentWidth <= 768) {
        enterSPMode();
        isSP = true;
      } else {
        enterPCMode();
        isSP = false;
      }
      return;
    }

    // 画面サイズ変更時にモードを切り替える処理
    if (currentWidth <= 768 && !isSP) {
      enterSPMode();
      isSP = true;
    } else if (currentWidth > 768 && isSP) {
      enterPCMode();
      isSP = false;
    }
  }

  // ウィンドウサイズ変更時にモード切り替え
  window.addEventListener('resize', function () {
    const currentWidth = window.innerWidth;
    if (currentWidth !== previousWidth) {
      switchMode(); // リサイズ時はモード判定のみ
      previousWidth = currentWidth;
    }
  });

  // 初回ロード時にモード切り替え
  switchMode(true); // 初回ロード時にモード判定
});



  </script>
		</div>
	</div>

</div>

<div class="pagetopArea"><a href="#">ページトップ</a></div>

<footer class="footerArea">
	<div class="footerGroupB">
		<div class="inner">
			<div class="footerMenu">
				<ul>
					<li><a href="/isa/sitemap.html">サイトマップ</a></li>
					<li><a href="/isa/copyright/index.html">リンク・著作権等について</a></li>
					<li><a href="/isa/publications/privacy/record.html">出入（帰）国記録に係る開示請求について</a></li>
				</ul>
			</div>
			<div class="footerAddress">
				<div class="logo"><p><img src="/isa/content/001358753.png" alt=""></p><br><br><br><br><p><img src="/isa/content/001358759.png" alt=""></p></div>
				<div class="group">
					<div class="ttl">出入国在留管理庁</div>
					<p>〒100-8973　東京都千代田区霞が関1-1-1　中央合同庁舎６号館<br>
					℡045-370-9755（代表） （法人番号：7000012030004）<br>
					※一般の方からの出入（帰）国記録、外国人登録原票等の開示請求についてのお問合せは総務課出入国情報開示係<br>
					℡03-5363-3005</p>
					<br>
					<div class="ttl">外国人在留支援センター（ＦＲＥＳＣ）</div>
					<p>〒160-0004　東京都新宿区四谷1-6-1　四谷タワー13階<br>
					℡0570-011000（代表）</p>
				</div>
			</div>
			<div class="footerCopyright">Copyright &copy; Immigration Services Agency of Japan All Rights Reserved.</div>
		</div>
	</div>
</footer>

</div><!-- / #wrapper -->

<script src="/isa/jquery-3.5.1.min.js"></script>
<script src="/isa/jquery-accessibleMegaMenu.js"></script>
<script src="/isa/fontsize.js"></script>
<script src="/isa/common_new.js"></script>
<script src="/isa/jquery.flexslider-min.js"></script>
<script src="/isa/top_new.js"></script>

<script src="//c.marsflag.com/mf/mfx/1.0-latest/js/mfx-sbox.js" charset="UTF-8"></script>
</body>
</html>